/**
 * 生成还款链接入参
 * @typedef {Object} GenerateRepayUrlParam
 * @property {RepayOrder[]} repayList - 还款列表
 * @property {number} totalAmount - 还款总金额
 * @property {number} repayType - 还款类型，1-正常还款，2-提前还款
 */

/**
 * 还款订单
 * @typedef {Object} RepayOrder
 * @property {number} payAmount - 还款金额
 * @property {number} [couponNo] - 优惠券id
 * @property {number} [reductionAmount] - 优惠金额
 * @property {RepayTerm[]} repayTerms - 还款期次列表
 */

/**
 * 还款期次
 * @typedef {Object} RepayTerm
 * @property {number} term - 期次
 * @property {number} amount - 还款金额
 */

/**
 * 生成还款链接数据传输对象
 * @typedef {Object} GenerateRepayUrlDto
 * @property {string} repayNo - 还款订单号
 * @property {string} [repayUrl] - 跳转链接
 */

/**
 * 还款结果查询数据传输对象
 * @typedef {Object} QueryRepayResultResponse
 * @property {number} repayStatus - 还款状态，1-还款中，2-还款成功，3-部分还款成功，4-还款失败
 * @property {string} [repayResult] - 返回失败原因
 * @property {number} [supprtReoffer] - 还款成功是否支持reoffer
 */

export const repayStatusMap = {
  Repaying: 1,
  Success: 2,
  PartialSuccess: 3,
  Failed: 4,
};

/**
 * 还款记录数据传输对象
 * @typedef {Object} RepayDto
 * @property {string} outOrderNo - 三方借款订单号
 * @property {number} repayAmount - 还款金额，单位：分
 * @property {string} repayTime - 实际还款时间，格式：yyyy-MM-dd HH:mm:ss
 * @property {number} repayStatus - 还款状态，1-还款中，2-还款成功，3-部分还款成功，4-还款失败
 * @property {string} [repayResult] - 返回失败原因
 */

/**
 * 还款试算请求参数对象 (对应后端 RepayTrialParam)
 *
 * @typedef {Object} RepayTrialParam
 * @property {number} [repayTerm] - 还款期数。
 *   - 最小值为 -1。
 *   - -1 表示所有期；1, 2, 3... 表示具体期数。
 * @property {number} [repayAmount] - 试算金额或本金，单位为分。
 *   - 最小值为 0。
 *   - 部分还款试算时必传。
 * @property {boolean} [repayPart] - 是否部分还款。
 * @property {string} [couponNo] - 优惠券 ID。
 * @property {string} [outOrderNo] - 渠道侧借款订单号。
 * @property {number} [budgetType] - 预算类型。
 *   - 1: 结清预算 - 去还款。
 *   - 2: 到期逾期预算 - 还某一期到期或还逾期。
 */

/**
 * 还款试算响应结果对象 (对应后端 RepayTrialResponse)
 *
 * @typedef {Object} RepayTrialResponse
 * @property {number} totalAmount - 总还款金额，单位：分。(荣耀对客展示的应还总额，是需要减去优惠券的)
 * @property {number} totalInterest - 总还款利息金额，单位：分。
 * @property {number} totalPrincipal - 总还款本金金额，单位：分。
 * @property {number} [totalOverdueFee] - 总还款逾期费，单位：分。
 * @property {number} [totalServiceFee] - 总还款服务费，单位：分。
 * @property {number} [totalViolateFee] - 提前结清违约金，单位：分。
 * @property {number} [totalChanges] - 总手续费，单位：分。
 * @property {number} [totalMgntFee] - 总管理费，单位：分。
 * @property {number} [totalDiscount] - 总优惠，单位：分。
 * @property {number} [totalPenalty] - 总罚息，单位：分。
 * @property {number} [totalInterAndFee] - 总计息费 (利息 + 服务费等)，单位：分。
 * @property {string} [remark] - 还款备注信息。
 * @property {string} [accountDate] - 账务日期，格式：yyyyMMdd。
 * @property {Array<CouponDto>} [usedCoupons] - 选中的优惠券信息列表。
 * @property {Array<CouponDto>} [usableCoupons] - 全部可用优惠券信息列表。
 * @property {Array<RepayPlanTermDto>} [repayPlanTerms] - 部分还款后剩余的还款计划列表。
 * @property {string} [nowDate] - 系统当前日期，格式：yyyyMMdd。
 * @property {Array<RepayTermDto>} repayTerms - 本次还款的期次明细列表。
 * @property {string} [reductionAmountDesc] - 优惠信息说明。
 */

/**
 * 优惠券信息对象 (对应后端 CouponDto)
 * @typedef {Object} CouponDto
 * @property {string} couponNo - 优惠券 ID。
 * @property {string} [couponRuleId] - 券模板 ID。
 * @property {string} discount - 总优惠金额 (字符串表示)。
 * @property {number} [effectiveTime] - 优惠券有效期起始时间戳 (毫秒)。
 * @property {number} [invalidTime] - 优惠券有效期截止时间戳 (毫秒)。
 */

/**
 * 还款计划期次明细对象 (部分还款后剩余的还款计划，对应后端 RepayPlanTermDto)
 * @typedef {Object} RepayPlanTermDto
 * @property {number} termNo - 期次。
 * @property {string} shouldRepayDate - 应还款日期，格式：yyyyMMdd。
 * @property {number} [termStatus=0] - 当期状态。
 *   - 1: 已结清
 *   - 2: 待还款
 *   - 3: 部分已还
 *   - 4: 逾期
 *   - 5: 宽限期
 * @property {number} [repayCategory] - 还款类型。
 *   - 1: 主动还款
 *   - 2: 银行卡代扣
 * @property {number} termAmount - 本期应还总额，单位：分。
 * @property {number} termPrincipal - 本期应还本金，单位：分。
 * @property {number} termInterest - 本期应还利息，单位：分。
 * @property {number} [termFee] - 本期应还服务费 (旧字段，可能与 `termServiceFee` 含义相似)，单位：分。
 * @property {number} termReductionAmount - 本期优惠券减免金额，单位：分。
 * @property {number} [termInterAndFee] - 本期息费 (利息 + 服务费等)，单位：分。
 * @property {number} [amount] - 应还金额 (与 `termAmount` 含义相似，可能用于特定场景)，单位：分。
 * @property {number} [termPenalty] - 本期应还罚息，单位：分。
 * @property {number} [termOverdueFee] - 本期应还逾期费，单位：分。
 * @property {number} [termViolateFee] - 本期应还违约金，单位：分。
 * @property {number} [termMgntFee] - 本期应还账务管理费，单位：分。
 * @property {number} [termServiceFee] - 本期应还服务费，单位：分。
 * @property {number} [termCharges] - 本期应还费用 (总计，包含服务费、管理费等)，单位：分。
 * @property {number} [termPrinPenalty] - 本期应还本金罚息，单位：分。
 * @property {number} [termInterPenalty] - 本期应还利息罚息，单位：分。
 * @property {number} [paidTime] - 本期实际还款日时间戳 (毫秒)。
 * @property {number} [paidTermAmount] - 本期实还总额，单位：分。
 * @property {number} [paidTermPrincipal] - 本期实还本金，单位：分。
 * @property {number} [paidTermInterest] - 本期实还利息，单位：分。
 * @property {number} [paidTermCharges] - 本期实还费用，单位：分。
 * @property {number} [paidTermReductionAmount] - 本期实还优惠券额，单位：分。
 * @property {number} [paidTermPrinPenalty] - 本期实还本金罚息，单位：分。
 * @property {number} [paidTermInterPenalty] - 本期实还利息罚息，单位：分。
 * @property {number} [paidTermOverdueFee] - 本期实还逾期费，单位：分。
 * @property {number} [paidTermFee] - 本期实还服务费 (旧字段)，单位：分。
 * @property {number} [paidTermServiceFee] - 本期实还服务费，单位：分。
 * @property {number} [paidTermMgntFee] - 本期实还账务管理费，单位：分。
 * @property {number} [paidTermViolateFee] - 本期实还违约金，单位：分。
 * @property {number} [paidInterAndFee] - 已还息费，单位：分。
 * @property {number} [payableTermAmount] - 剩余应还还款金额，单位：分。
 * @property {number} [payableTermPrincipal] - 剩余应还还款本金，单位：分。
 * @property {number} [payableTermInterest] - 剩余应还还款利息，单位：分。
 * @property {number} [payableTermCharges] - 剩余应还还款费用，单位：分。
 * @property {number} [payableTermPrinPenalty] - 剩余应还本金罚息，单位：分。
 * @property {number} [payableTermInterPenalty] - 剩余应还利息罚息，单位：分。
 * @property {number} [payableTermOverdueFee] - 剩余应还逾期费，单位：分。
 * @property {number} [payableTermFee] - 剩余应还服务费 (旧字段)，单位：分。
 * @property {number} [payableTermServiceFee] - 剩余应还服务费，单位：分。
 * @property {number} [payableTermMgntFee] - 剩余应还账务管理费，单位：分。
 * @property {number} [payableTermViolateFee] - 剩余应还违约金，单位：分。
 * @property {number} [payableInterAndFee] - 剩余应还息费，单位：分。
 * @property {number} [overdueDays] - 逾期天数。
 * @property {number} [overdueAmt] - 逾期金额 (本金+利息+罚息)，单位：分。
 * @property {boolean} [preRepay] - 是否提前还款 (原描述 "逾期金额=本金+利息+罚息" 有误，结合上下文修正)。
 * @property {boolean} [overdue] - 是否逾期。
 * @property {number} [earlyRepayDays] - 提前还款天数。
 * @property {number} [paidTermPenalty] - 本期实还罚息，单位：分。
 * @property {number} [payableTermPenalty] - 剩余应还罚息，单位：分。
 * @property {number} [overdueAmount] - 逾期金额 (与 `overdueAmt` 含义相似，可能用于特定场景)，单位：分。
 * @property {number} [loanOverDueDays] - 逾期天数，兼容度小满 (与 `overdueDays` 含义相似，可能用于特定场景)。
 * @property {number} [termTempDiscount] - 本期限时降价优惠金额，单位：分。
 * @property {number} [termScheduleDiscount] - 本期按期还优惠金额，单位：分。
 * @property {number} [termTotalDiscount] - 本期总优惠金额，单位：分。
 * @property {string} [reductionAmountDesc] - 优惠信息说明。
 */

/**
 * 本次还款期次明细对象 (对应后端 RepayTermDto)
 * @typedef {Object} RepayTermDto
 * @property {number} termNo - 期次。
 * @property {number} termAmount - 本期还款总额,单位：分。
 * @property {number} termPrincipal - 本期还款本金，单位：分。
 * @property {number} termInterest - 本期还款利息，单位：分。
 * @property {number} [termReductionAmount] - 本期还款优惠券减免金额，单位：分。
 * @property {number} [termPenalty] - 本期还款罚息，单位：分。
 * @property {number} [termPrinPenalty] - 本期还款本金罚息，单位：分。
 * @property {number} [termInterPenalty] - 本期还款利息罚息，单位：分。
 * @property {number} [termOverdueFee] - 本期还款逾期费，单位：分。
 * @property {number} [termViolateFee] - 本期还款违约金，单位：分。
 * @property {number} [termServiceFee] - 本期还款服务费，单位：分。
 * @property {number} [termInterAndFee] - 本期息费 (利息 + 服务费等)，单位：分。
 * @property {number} [overdueDays] - 逾期天数。
 */


/**
 * 还款参数模型
 *
 * @typedef {Object} RepayParam
 * @property {number} totalAmount - 总还款金额，单位：分，必须大于等于0
 * @property {number} repayType - 还款类型，1-提前还款，2-正常还款
 * @property {boolean} repayPart - 是否部分还款
 * @property {string} bankCardId - 还款卡绑卡ID，不能为空
 * @property {string} smsCode - 短信验证码，不能为空
 * @property {string} [serialNo] - 验证码序列号（云侧发送短信验证码有返回时需要）
 * @property {boolean} needResign - 是否需要重新签约标识
 * @property {RepayItemParam[]} repayItemList - 还款列表，最多50条
 * @property {string} [budgetType] - 预算类型 (1--结清预算 2--逾期到期预算)
 * @property {number} [budgetAmount] - 预算时返回的总金额（还款试算不传金额时返回的总金额）
 * @property {number} [budgetInterest] - 预算时返回的利息（还款试算不传金额时返回的利息）
 * @property {number} [budgetPrincipal] - 预算时返回的本金（还款试算不传金额时返回的本金）
 * @property {number} [budgetPenalty] - 预算时返回的罚息（还款试算不传金额时返回的罚息）
 */

/**
 * 还款项参数模型
 *
 * @typedef {Object} RepayItemParam
 * @property {string} outOrderNo - DXM借款订单号，不能为空
 * @property {number} repayAmount - 还款总额，必须大于等于0
 * @property {string} [couponNo] - 优惠券id
 * @property {number} [reductionAmount] - 优惠金额，单位：分
 * @property {RepayTermParam[]} [repayTerms] - 还款期次列表，主动还款必传，提前还款无需传
 */

/**
 * 还款期次参数模型
 *
 * @typedef {Object} RepayTermParam
 * @property {number} termNo - 期次，不能为空
 * @property {number} termAmount - 还款金额，不能为空
 */


/**
 * 还款响应模型
 *
 * @typedef {Object} RepayResponse
 * @property {string} [repayNo] - 还款交易订单号
 * @property {string} [orderInfo] - 订单信息（接入蚂蚁新增）
 */



