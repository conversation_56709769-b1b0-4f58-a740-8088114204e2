import { request } from "../helpers/utils";

/**
 * 银行卡转账还款
 * @param {GenerateRepayUrlParam} data - 银行卡转账还款入参
 * @returns {Promise<BaseResponse<GenerateRepayUrlDto>>} - 银行卡转账还款返回参数
 */
export async function transferRepay(data) {
  return request("/loan/api/repay/transfer", { encryptedParams: data });
}

/**
 * 还款结果查询
 * @param {string} repayNo - 还款订单号
 * @returns {Promise<BaseResponse<QueryRepayResultResponse>>} - 还款结果查询数据返回参数
 */
export async function repayStatusQuery(repayNo) {
  return request("/loan/api/repay/queryStatus", { repayNo });
}

/**
 * 还款记录查询
 * @param outOrderNo
 * @param repayNo
 * @returns {Promise<BaseResponse<RepayRecordDto[]>>}
 */
export async function repayRecordQuery(outOrderNo, repayNo) {
  return request("/loan/api/repay/record", { outOrderNo, repayNo });
}

/**
 * 还款试算
 * @param {RepayTrialParam} params - 还款试算入参
 * @returns {Promise<BaseResponse<RepayTrialResponse>>} - 还款试算返回参数
 */
export async function repayTrial(params) {
  return request("/loan/api/repay/trial", params);
}

/**
 * 提交还款
 * @param {RepayParam} params - 提交还款入参
 * @returns {Promise<BaseResponse<RepayResponse>>} - 提交还款返回参数
 */
export async function repaySubmit(params) {
  return request("/loan/api/repay/submit", { encryptedParams: params });
}
