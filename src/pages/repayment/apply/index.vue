// 还款页
<script setup>
import { onServerPrefetch, ref, onMounted, computed } from "vue";
import Big from "big.js";
import useStore from "./store";
import { initStore, amountNumberFormat, formatDateToYYYYMMDD } from "../../../helpers/utils";
import { goto, back, report } from "../../../helpers/native-bridge";
import { setWithExactExpireUnit, del } from "../../../helpers/storage";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";

const { store, data } = initStore(useStore);
// 下方滚动区
const scontainer = ref(null);
onServerPrefetch(store.initial);

const hnrbar = ref(null);
const headside = ref(null);
const bottom = ref(null);
const showViolateFeeBubble4 = ref(false);
const showViolateFeeBubble5 = ref(false);

const showBtn1 = computed(() => {
  return (
    (data.value.isdelay && data.value.supportRepayType?.find((item) => item.type === 4)) ||
    (!data.value.isdelay &&
      (data.value.supportRepayType?.find((item) => item.type === 1) ||
        data.value.supportRepayType?.find((item) => item.type === 2)))
  );
});

const btn1Disabled = computed(() => {
  return (
    (data.value.isdelay &&
      data.value.supportRepayType?.find((item) => item.type === 4 && item.disabled)) ||
    (!data.value.isdelay &&
      (data.value.supportRepayType?.find((item) => item.type === 1 && item.disabled) ||
        data.value.supportRepayType?.find((item) => item.type === 2 && item.disabled)))
  );
});

const allSquaredBtn1Disabled = computed(() => {
  return (
    !data.value.isdelay &&
    data.value.supportRepayType?.find((item) => item.type === 2 && item.disabled)
  );
});

const showBtn2 = computed(() => {
  return (
    (data.value.isdelay && data.value.supportRepayType?.find((item) => item.type === 5)) ||
    (!data.value.isdelay && data.value.supportRepayType?.find((item) => item.type === 3)) ||
    data.value.supportRepayType?.find((item) => item.type === 6)
  );
});

const btn2Disabled = computed(() => {
  return (
    (data.value.isdelay &&
      data.value.supportRepayType?.find((item) => item.type === 5 && item.disabled)) ||
    (!data.value.isdelay &&
      data.value.supportRepayType?.find((item) => item.type === 3 && item.disabled))
  );
});

const isStandardWidth = computed(() => {
  return !(showBtn1.value && showBtn2.value);
});

const startWeb = ref("hidden");
onMounted(async () => {
  if (data.value.isTokenExpired) {
    await store.initial();
  }
  del(`U_REPAYTERMS_${data.value.param.userId}`);
  setWithExactExpireUnit(`U_LOAN_ORDER_${data.value.param.outOrderNo}`, data.value.resdata, 1, "D");
  setTimeout(() => {
    startWeb.value = "visible";
  });
  del(`U_REPAYMENT_SMS_REPAYNO_${data.value.param.userId}`);
  const res = await getCurrentCpInfo();
  // 是否凌晨自动还款
  data.value.deductionEarlyMorning = res.deductionEarlyMorning;
  data.value.showDueTip = res.showDueTip;
  data.value.repayViolateFeeExplanation = res?.repayViolateFeeExplanation;
  data.value.interestFeeExplanation = res?.interestFeeExplanation;
  report("wallet_page_view", {
    page_name: "repayment_apply_page",
    supportRepayType: store.parseSupportRepayType(store.supportRepayType),
    supportRepay: store.getSupportRepayEnable(store.supportRepayType),
  });
});
function onClickLeft() {
  back();
}
// 本期
function gotodetail() {
  if (store.btn2 === "去还款") {
    // 是否逾期且为下一期还款日
    let isOverdueAndOnNextPeriodRepayDay = false;
    if (data.value.isdelay) {
      if (store.repayPlanInfo?.repayPlanTerms && store.repayPlanInfo?.repayPlanTerms.length > 0) {
        // 找到当前期次对应的还款计划
        const currentTermPlan = store.repayPlanInfo?.repayPlanTerms.find(
          (term) => term.termNo === store.repayPlanInfo?.currentTerm,
        );
        if (currentTermPlan) {
          // 判断当前日期是否与当前期次的应还款日期匹配
          const todayFormatted = formatDateToYYYYMMDD(new Date());
          if (todayFormatted === currentTermPlan.shouldRepayDate) {
            isOverdueAndOnNextPeriodRepayDay = true;
          }
        }
      }
    }

    goto(
      "/wallet-loan-web/pages/repayment/settleForSsd?" +
        `outOrderNo=${data.value.param.outOrderNo}&` +
        `budgetType=${data.value.budgetType}&` +
        `isDelay=${data.value.isdelay}&` +
        `delayDays=${data.value.delaydays}&` +
        `isOverdueAndOnNextPeriodRepayDay=${isOverdueAndOnNextPeriodRepayDay}`,
    );
  } else if (data.value.isdelay) {
    // 还全部逾期
    goto(
      "/wallet-loan-web/pages/repayment/trial?isdelay=true&" +
        `delaydays=${data.value.delaydays}&` +
        // `allTerm=${data.value.delayinfos.repayTerm}&`+
        `outOrderNo=${data.value.param.outOrderNo}&` +
        `repayTerm=${data.value.delayinfos.repayTerm}&` +
        `repayAmount=${data.value.senddelayamt}&` +
        `repayPart=false&random=${data.value.random}&` +
        `serviceFee=${data.value.delayinfos.serviceFee}&` +
        `repayType=${store.btn2}`,
    );
  } else if (data.value.clear) {
    goto(
      "/wallet-loan-web/pages/repayment/trial?isdelay=false&" +
        `allTerm=${data.value.totalTerm}&` +
        `outOrderNo=${data.value.param.outOrderNo}&` +
        `repayTerm=${data.value.active1?.repayPlanTerms[0]?.termNo}&` +
        `repayAmount=${data.value.active1?.repayPlanTerms[0]?.sendamt}&` +
        `repayPart=false&random=${data.value.random}&` +
        `RepayDate=${data.value.active1?.repayPlanTerms[0]?.shouldRepayDate}&` +
        `serviceFee=${data.value.active1?.repayPlanTerms[0]?.termServiceFee}&` +
        `repayType=${store.btn2}`,
    );
  } else {
    goto(
      "/wallet-loan-web/pages/repayment/trial?isdelay=false&" +
        `allTerm=${data.value.totalTerm}&` +
        `outOrderNo=${data.value.param.outOrderNo}&` +
        `repayTerm=${data.value.issuedetail.termNo}&` +
        `repayAmount=${data.value.issuedetail.sendamt}&` +
        `repayPart=false&random=${data.value.random}&` +
        `RepayDate=${data.value.issuedetail?.shouldRepayDate}&` +
        `serviceFee=${data.value.issuedetail?.termServiceFee}&` +
        `repayType=${store.btn2}`,
    );
  }
  report("wallet_page_click", {
    click_name: "repayment_apply_repay_click",
    repayType: store.btn2,
  });
}

// 全部
function gotosettle() {
  if (data.value.isdelay) {
    // 还部分逾期
    goto(
      "/wallet-loan-web/pages/repayment/settle?isdelay=true&" +
        `delaydays=${data.value.delaydays}&` +
        `outOrderNo=${data.value.param.outOrderNo}&` +
        `repayTerm=${data.value.delayinfos.repayTerm}&` +
        `repayAmount=${data.value.senddelayamt}&` +
        `repayPart=false&random=${data.value.random}&` +
        `period=${data.value.delayinfos.repayTerm}&` +
        `repayType=${store.btn1}`,
    );
  } else if (data.value.supportRepayType.find((item) => item.type === 2)) {
    let period = data.value.active1?.repayPlanTerms?.length;
    let repayTerm = data.value.active1?.repayPlanTerms[0]?.termNo;
    let { sendtotalamt } = data.value;
    if (data.value.issuedetail.termStatus !== 1) {
      period += 1;
      repayTerm = data.value.issuedetail.termNo;
      sendtotalamt = Big(sendtotalamt || 0).plus(Number(data.value.issuedetail.sendamt));
    }
    // 全部结清，跳转trial页面
    goto(
      "/wallet-loan-web/pages/repayment/settle?isdelay=false&" +
        `allTerm=${data.value.totalTerm}&` +
        `outOrderNo=${data.value.param.outOrderNo}&` +
        `repayTerm=${repayTerm}&` +
        `repayAmount=${sendtotalamt}&` +
        "repayPart=false&" +
        "random=false&" +
        `period=${period}&` +
        `repayType=${store.btn1}`,
    );
  } else if (data.value.isLastTerm) {
    // 提前还款，跳转settle页面
    goto(
      "/wallet-loan-web/pages/repayment/settle?isdelay=false&" +
        `outOrderNo=${data.value.param.outOrderNo}&` +
        `repayTerm=${data.value.issuedetail.termNo}&` +
        `repayAmount=${data.value.issuedetail.sendamt}&` +
        "repayPart=false&" +
        `random=${data.value.random}&` +
        `period=${data.value.issuedetail.termNo}&` +
        `repayType=${store.btn1}`,
    );
  } else {
    goto(
      "/wallet-loan-web/pages/repayment/settle?isdelay=false&" +
        `outOrderNo=${data.value.param.outOrderNo}&` +
        `repayTerm=${data.value.active1?.repayPlanTerms[0]?.termNo}&` +
        `repayAmount=${data.value.sendtotalamt}&` +
        "repayPart=false&" +
        `random=${data.value.random}&` +
        `period=${data.value.active1?.repayPlanTerms?.length}&` +
        `repayType=${store.btn1}`,
    );
  }
  report("wallet_page_click", {
    click_name: "repayment_apply_repay_click",
    repayType: store.btn1,
  });
}
</script>

<template>
  <div class="bg" :style="{ visibility: startWeb }">
    <hnr-nav-bar ref="hnrbar" transparent="true" class="nav-padding-top" title="还款">
      <template #left>
        <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
      </template>
    </hnr-nav-bar>
    <div ref="headside" class="container">
      <span v-if="!data.isdelay" class="infotitle1"
        >{{ data.issuedetail.termNo }}/{{ data.totalTerm }}期(本期)应还</span
      >
      <span v-if="data.isdelay" class="infotitle1" style="color: var(--hnr-color-error)"
        >已逾期{{ data.delaydays }}天，逾期欠款</span
      >
      <div v-if="!data.clear" style="display: contents">
        <span class="infotitle2"
          >￥{{
            amountNumberFormat(data.isdelay ? data.delayamt : data.issuedetail.termAmount)
          }}</span
        >
        <span v-show="!data.isdelay" class="infotitle3"
          >系统将于还款日{{ data.issuedetail?.shouldRepayDate?.substring(5)
          }}<span v-if="data.deductionEarlyMorning === 1">(凌晨)</span>自动扣款</span
        >
        <span v-show="data.isdelay" class="infotitle3"
          >逾期后，每日<span v-if="data.deductionEarlyMorning === 1">(凌晨)</span>将自动扣款</span
        >
      </div>
      <div v-else style="margin-bottom: var(--hnr-elements-margin-vertical-M2)">
        <span class="infotitle2">已还清</span>
        <span class="infotitle4"
          >(￥{{ amountNumberFormat(data.issuedetail.paidTermAmount || 0) }})</span
        >
      </div>
      <hnr-divider class="topdivider" line />
      <!-- 逾期还款明细 -->
      <hnr-collapse
        v-show="data.isdelay"
        v-model="data.activeNames12"
        class="current-period-collapse"
        accordion
      >
        <hnr-collapse-item class="current-period" name="duePeriod">
          <template #title>
            <div class="current-period-title">逾期还款明细</div>
          </template>
          <div class="cellLine current-cellLine">
            <div class="cellLeft">还款本金</div>
            <div class="cellRight">{{ "￥" + amountNumberFormat(data.delayinfos.Principal) }}</div>
          </div>
          <div v-if="parseFloat(data.delayinfos.interAndFee)" class="cellLine">
            <div class="cellLeft" style="display: flex">
              息费
              <hnr-bubble-tip
                v-model:show="showViolateFeeBubble4"
                :message="data.interestFeeExplanation"
                placement="bottom"
                class="violateFee-bubble-tip"
              >
                <template #reference>
                  <hnr-icon>
                    <img src="/loan/helps.svg" alt class="imgs" />
                  </hnr-icon>
                </template>
              </hnr-bubble-tip>
            </div>
            <div class="cellRight">
              {{ "￥" + amountNumberFormat(data.delayinfos.interAndFee) }}
            </div>
          </div>
          <div v-else>
            <div class="cellLine">
              <div class="cellLeft">利息</div>
              <div class="cellRight">{{ "￥" + amountNumberFormat(data.delayinfos.Interest) }}</div>
            </div>
            <div v-if="parseFloat(data.delayinfos.serviceFee)" class="cellLine">
              <div class="cellLeft" style="display: flex">
                <span>服务费</span>
                <hnr-bubble-tip
                  v-model:show="showViolateFeeBubble4"
                  :message="data.repayViolateFeeExplanation"
                  placement="bottom"
                  class="violateFee-bubble-tip"
                >
                  <template #reference>
                    <hnr-icon>
                      <img src="/loan/helps.svg" alt class="imgs" />
                    </hnr-icon>
                  </template>
                </hnr-bubble-tip>
              </div>
              <div class="cellRight">
                {{ "￥" + amountNumberFormat(data?.delayinfos?.serviceFee) }}
              </div>
            </div>
          </div>
          <div class="cellLine">
            <div class="cellLeft">逾期期数</div>
            <div class="cellRight cellRight-wrap" style="text-align: right; max-width: 60%">
              <span
                v-for="(item, index) in data.delayinfos.Terms"
                :key="item"
                style="white-space: nowrap"
                >{{ item }}期<template v-if="index !== data.delayinfos.Terms.length - 1"
                  >,</template
                ></span
              >
            </div>
          </div>
          <div v-if="data.delayinfos.OverdueFee !== '0'" class="cellLine">
            <div class="cellLeft">逾期罚息</div>
            <div class="cellRight">
              {{ "￥" + amountNumberFormat(data.delayinfos.OverdueFee || 0) }}
            </div>
          </div>
          <div v-if="!Big(data.delayinfos?.ReductionAmount || 0).eq(0)" class="cellLine">
            <div class="cellLeft">优惠券</div>
            <div class="cellRight">
              {{ "-￥" + amountNumberFormat(data.delayinfos.ReductionAmount || 0) }}
            </div>
          </div>
        </hnr-collapse-item>
      </hnr-collapse>
      <!-- 非逾期本期还款明细 -->
      <hnr-collapse
        v-show="!data.isdelay"
        v-model="data.activeNames11"
        class="current-period-collapse"
        accordion
      >
        <hnr-collapse-item class="current-period" name="currentPeriod">
          <template #title>
            <div class="current-period-title">本期还款明细</div>
          </template>
          <div class="cellLine current-cellLine">
            <div class="cellLeft">还款本金</div>
            <div class="cellRight">
              {{
                "￥" +
                amountNumberFormat(
                  data.clear ? data.issuedetail.paidTermPrincipal : data.issuedetail.termPrincipal,
                )
              }}
            </div>
          </div>
          <div
            v-if="
              parseFloat(data?.issuedetail?.termInterAndFee) ||
              parseFloat(data.issuedetail.paidInterAndFee)
            "
            class="cellLine"
          >
            <div class="cellLeft" style="display: flex; align-items: center">
              本期息费
              <hnr-bubble-tip
                v-model:show="showViolateFeeBubble5"
                :message="data.interestFeeExplanation"
                placement="bottom"
                class="violateFee-bubble-tip"
              >
                <template #reference>
                  <hnr-icon>
                    <img src="/loan/helps.svg" alt class="imgs" />
                  </hnr-icon>
                </template>
              </hnr-bubble-tip>
            </div>
            <div class="cellRight">
              {{
                "￥" +
                amountNumberFormat(
                  data.clear ? data.issuedetail.paidInterAndFee : data.issuedetail.termInterAndFee,
                )
              }}
            </div>
          </div>
          <div v-else>
            <div class="cellLine">
              <div class="cellLeft">本期利息</div>
              <div class="cellRight">
                {{
                  "￥" +
                  amountNumberFormat(
                    data.clear ? data.issuedetail.paidTermInterest : data.issuedetail.termInterest,
                  )
                }}
              </div>
            </div>
            <div
              v-if="
                parseFloat(data?.issuedetail?.termServiceFee) ||
                parseFloat(data.issuedetail.paidTermServiceFee)
              "
              class="cellLine"
            >
              <div class="cellLeft" style="display: flex; align-items: center">
                服务费
                <hnr-bubble-tip
                  v-model:show="showViolateFeeBubble5"
                  :message="data.repayViolateFeeExplanation"
                  placement="bottom"
                  class="violateFee-bubble-tip"
                >
                  <template #reference>
                    <hnr-icon>
                      <img src="/loan/helps.svg" alt class="imgs" />
                    </hnr-icon>
                  </template>
                </hnr-bubble-tip>
              </div>
              <div class="cellRight">
                {{
                  "￥" +
                  amountNumberFormat(
                    data.clear
                      ? data.issuedetail.paidTermServiceFee
                      : data.issuedetail.termServiceFee,
                  )
                }}
              </div>
            </div>
          </div>
          <div v-if="data.issuedetail.paidPenalty !== '0' && data.clear" class="cellLine">
            <div class="cellLeft">逾期罚息</div>
            <div class="cellRight">
              {{ "￥" + amountNumberFormat(data.issuedetail.paidPenalty || 0) }}
            </div>
          </div>
          <div v-if="!Big(data.issuedetail?.termReductionAmount || 0).eq(0)" class="cellLine">
            <div class="cellLeft">优惠金额</div>
            <div class="cellRight">
              {{ "-￥" + amountNumberFormat(data.issuedetail.termReductionAmount || 0) }}
            </div>
          </div>
          <hnr-divider
            v-if="data.issuedetail?.reductionAmountDesc"
            class="topdivider"
            style="
              margin-top: var(--dp12);
              height: var(--dp1) !important;
              border-width: none !important;
            "
            line
          />
          <div
            v-if="data.issuedetail?.reductionAmountDesc"
            class="cellLine"
            style="font-size: var(--hnr-caption); color: var(--hnr-text-color-tertiary)"
            v-html="data.issuedetail?.reductionAmountDesc"
          ></div>
        </hnr-collapse-item>
      </hnr-collapse>
    </div>
    <hnr-divider style="height: 8px; border-width: 0; border-color: transparent"></hnr-divider>
    <span v-if="data.active1?.repayPlanTerms?.length > 0" class="detailhead">{{
      "未来待还" +
      data.active1?.repayPlanTerms?.length +
      "期，待还总额￥" +
      amountNumberFormat(data.totalamount)
    }}</span>
    <hnr-cell-group class="scroll-container">
      <hnr-collapse
        v-if="data.active1.repayPlanTerms.length > 0"
        ref="scontainer"
        v-model="data.activeNames2"
        accordion
      >
        <div v-for="(i, index) in data.active1?.repayPlanTerms" :key="i.termNo">
          <hnr-collapse-item :label="i.label" :name="index">
            <template #title>
              <div class="downtitle">{{ i.title }}</div>
              <div class="downlititle">
                剩余待还{{ "￥" + amountNumberFormat(i.termAmount) }}，还款日{{
                  i.shouldRepayDate.substring(5)
                }}
              </div>
            </template>
            <div class="cellLine">
              <div class="cellRight">还款明细</div>
            </div>
            <div class="cellLine">
              <div class="cellLeft">还款本金</div>
              <div class="cellRight">{{ "￥" + amountNumberFormat(i.termPrincipal) }}</div>
            </div>
            <div v-if="parseFloat(i?.termInterAndFee)" class="cellLine">
              <div class="cellLeft">本期息费</div>
              <div class="cellRight">{{ "￥" + amountNumberFormat(i.termInterAndFee) }}</div>
            </div>
            <div v-else>
              <div class="cellLine">
                <div class="cellLeft">本期利息</div>
                <div class="cellRight">{{ "￥" + amountNumberFormat(i.termInterest) }}</div>
              </div>
              <div v-if="parseFloat(i?.termServiceFee)" class="cellLine">
                <div class="cellLeft">服务费</div>
                <div class="cellRight">{{ "￥" + amountNumberFormat(i?.termServiceFee || 0) }}</div>
              </div>
            </div>
            <div v-if="i.termPenalty !== '0.00'" class="cellLine">
              <div class="cellLeft">逾期罚息</div>
              <div class="cellRight">{{ "￥" + amountNumberFormat(i.termPenalty || 0) }}</div>
            </div>
            <div v-if="!Big(i.termReductionAmount || 0).eq(0)" class="cellLine">
              <div class="cellLeft">优惠金额</div>
              <div class="cellRight">
                {{ "-￥" + amountNumberFormat(i.termReductionAmount || 0) }}
              </div>
            </div>
          </hnr-collapse-item>
          <hnr-divider
            v-if="index !== data.active1.repayPlanTerms.length - 1"
            class="downdivider"
            line
          />
        </div>
      </hnr-collapse>
    </hnr-cell-group>
    <div ref="bottom" :class="data.istoday || btn1Disabled ? 'applybottom1' : 'applybottom'">
      <div v-if="data.istoday" class="bottomtitle">当前暂不支持还款</div>
      <div v-if="!data.istoday && allSquaredBtn1Disabled" class="bottomtitle">
        当前为还款日，还清本期后可结清剩余借款
      </div>
      <div v-if="data.showDueTip === 0 && data.isdelay && data.currentDueDate" class="bottomtitle">
        还清逾期借款后，可还本期借款
      </div>
      <div
        v-if="data.showDueTip === 0 && data.isdelay && !data.currentDueDate && !data.istoday"
        class="bottomtitle"
      >
        还清逾期借款后，可结清剩余借款
      </div>
      <div v-if="!data.clear" class="footer">
        <hnr-button
          v-if="showBtn1"
          type="default"
          class="bottom-btn"
          :standard-width="isStandardWidth"
          :disabled="data.istoday || btn1Disabled"
          @click="gotosettle"
          >{{ data.btn1 }}</hnr-button
        >
        <hnr-button
          v-if="showBtn2"
          type="primary"
          class="bottom-btn"
          :standard-width="isStandardWidth"
          :disabled="data.istoday || btn2Disabled"
          @click="gotodetail"
          >{{ data.btn2 }}</hnr-button
        >
      </div>
      <div v-else class="footer">
        <hnr-button
          v-if="showBtn1"
          type="default"
          class="content-bottom-btn"
          :standard-width="isStandardWidth"
          :disabled="data.istoday || btn1Disabled"
          @click="gotosettle"
          >{{ data.btn1 }}</hnr-button
        >
        <hnr-button
          v-if="showBtn2"
          type="primary"
          class="bottom-btn"
          :standard-width="isStandardWidth"
          :disabled="data.istoday || btn2Disabled"
          @click="gotodetail"
          >{{ data.btn2 }}</hnr-button
        >
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 深色模式下的图标反色处理 */
@media (prefers-color-scheme: dark) {
  .imgs {
    filter: invert(1);
  }
}

.imgs {
  vertical-align: middle;
  margin-bottom: var(--dp3);
  width: var(--dp14);
  height: var(--dp14);
  transform: translateY(2px);
}

.bg {
  user-select: none;
  display: flex;
  flex-direction: column;
  position: fixed;
  background: var(--hnr-color-background-cardview);
  width: 100%;
  height: 100%;
}
.container {
  display: flex;
  flex-direction: column;
  margin: var(--hnr-elements-margin-vertical-M2);
  background-color: var(--hnr-color-list-card-background);
  border-radius: var(--hnr-card-border-radius);
}
.topdivider {
  margin: 0 var(--hnr-list-card-padding-end) 0 var(--hnr-list-card-padding-start);
  width: auto;
}
.downdivider {
  margin: var(--dp3) var(--hnr-list-card-padding-end) var(--dp3) var(--hnr-list-card-padding-start);
  width: auto;
}
.infotitle1 {
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.infotitle2 {
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
}
.infotitle3 {
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-M);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.scroll-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-top: var(--hnr-elements-margin-vertical-M2);
  width: 100%;
  overflow-y: auto;
  max-height: calc(100vh - 22rem);
  border-radius: var(--hnr-list-card-corner-radius);
  background-color: transparent !important;
}
.scroll-container::-webkit-scrollbar {
  width: var(--dp3);
  -ms-overflow-style: scrollbar;
  scrollbar-width: thin;
}

.scroll-container::-webkit-scrollbar-thumb {
  border-radius: var(--dp3);
  background-color: #ccc;
}

/* 默认隐藏滚动条 */
.hnr-collapse {
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

.hnr-collapse::-webkit-scrollbar {
  width: var(--dp3);
  -ms-overflow-style: scrollbar;
  scrollbar-width: thin;
  display: none; /* Chrome/Safari */
}

:deep(.hnr-collapse) {
  background-color: var(--hnr-color-list-card-background);
  margin: 0;
  padding: var(--hnr-elements-margin-vertical-M2) 0;
}
.infotitle4 {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
}
.cellGroup {
  max-height: auto;
  transition: height 0.3s;
  padding: var(--hnr-elements-margin-vertical-M2) 0 var(--hnr-elements-margin-vertical-L);
}
.cellGroup:hover {
  max-height: var(--dp200);
}
.cellLine1 {
  width: 100%;
  position: relative;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  justify-content: space-between;
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-start);
  margin-top: 0;
  padding-top: 0;
}
.cellLine {
  width: 100%;
  position: relative;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  justify-content: space-between;
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-start);
  padding-top: var(--hnr-elements-margin-vertical-M2);
}
.cellLeft {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.cellRight {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  display: flex;
  align-items: center;
}
.cellRight-wrap {
  flex-wrap: wrap;
  justify-content: end;
}
.applybottom {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  width: 100%;
  background-color: var(--hnr-color-background-cardview);
  padding-top: var(--hnr-elements-margin-vertical-L);
}
.applybottom1 {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
  width: 100%;
  /* height: var(--dp104); */
  background-color: var(--hnr-color-background-cardview);
  padding-top: var(--hnr-elements-margin-vertical-L);
}
:deep(.hnr-collapse-item .hnr-cell) {
  padding: 0 var(--hnr-elements-margin-vertical-M2) !important;
}

:deep(.hnr-cell-group--disabledCellMargin .hnr-cell-box) {
  width: auto !important;
}
.detailhead {
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
  margin-left: var(--hnr-elements-margin-vertical-M2);
  padding-left: var(--hnr-list-card-padding-start);
  color: var(--hnr-text-color-primary);
}

.bottomtitle {
  color: var(--hnr-text-color-primary);
  font-weight: var(--hnr-font-weight-regular);
  font-size: var(--hnr-body-3);
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.footer {
  width: 100%;
  text-align: center;
  z-index: 100;
  display: flex;
  justify-content: center;
  padding: 0 var(--hnr-max-padding-start) 0 var(--hnr-max-padding-end);
  box-sizing: border-box;
}
.bottom-btn {
  margin-bottom: var(--hnr-default-padding-bottom-fixed);
}
.bottom-btn:nth-child(2) {
  margin-left: 12px;
}
.leftcell {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.content-bottom-btn {
  max-width: none !important;
  margin: 0 0 var(--hnr-default-padding-bottom-fixed) 0;
}
.downlititle {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.downtitle {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-XS);
}
.current-period-collapse {
  padding: 8px 0;
}
.current-period :deep(.hnr-cell-box) {
  min-height: auto !important;
  line-height: 1;
}
.current-period-title {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.current-period :deep(.hnr-collapse-item__content) {
  padding-bottom: 8px;
}
.current-cellLine {
  padding-top: 6px;
}
:deep(.hnr-icon) {
  height: auto;
}
:deep(.hnr-icon img) {
  width: var(--hnr-icon-width);
}
:deep(.hnr-collapse-item__content) {
  padding: 0;
}

:deep(.hnr-collapse-item__title .hnr-cell__right-icon svg) {
  width: var(--dp16);
  color: var(--hnr-color-quaternary);
  transform: translateX(var(--dp6));
}
:deep(.hnr-cell__title) {
  color: var(--hnr-text-color-primary);
  font: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
:deep(.hnr-cell--center .hnr-cell__left) {
  padding: 0 !important;
}
:deep(.hnr-cell__value) {
  margin: 0;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
:deep(.hnr-cell__left) {
  color: var(--hnr-text-color-secondary);
  font: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  max-width: calc(100% - var(--hnr-elements-margin-horizontal-L2)) !important;
}
.hnr-basic-width-margin {
  margin-left: calc(24px - var(--hnr-list-card-padding-end)) !important;
  margin-right: calc(24px - var(--hnr-list-card-padding-end)) !important;
  width: calc(
    (100% - 48px) + var(--hnr-list-card-padding-end) + var(--hnr-list-card-padding-start)
  ) !important;
}
/* :deep(.icon-upDown svg g g) {
  fill: transparent;
}
:deep(.icon-upDown svg g g path) {
  fill: var(--hnr-color-quaternary);
  fill-opacity: 0.8;
} */
:deep(.icon-upDown svg) {
  width: var(--dp16);
}
:deep(.icon-upDown svg path) {
  fill: var(--hnr-color-quaternary);
  fill-opacity: 0.8;
}
:deep(.hnr-cell__right-icon--icon--wider svg g g path) {
  fill: var(--hnr-color-quaternary);
  fill-opacity: 0.8;
}
</style>
