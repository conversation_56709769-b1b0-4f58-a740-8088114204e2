import { defineStore } from "pinia";
import Big from "big.js";
import { repayTrial } from "../../../api/repay";
import { convertFenToYuan } from "../../../helpers/mathUtils";

export default defineStore("repayment/settleForSsd", {
  state: () => ({
    param: {},
    firstTrialInfo: {},
    trialInfo: {},
    repayAmountStr: "",
    totalAmountInYuan: 0,
    totalPrincipalInYuan: 0,
    totalInterestInYuan: 0,
  }),
  actions: {
    async initial() {
      const [trialResp] = await Promise.all([
        repayTrial({
          outOrderNo: this.param.outOrderNo,
          budgetType: this.param.budgetType,
        }),
      ]);

      if (trialResp?.code === 0) {
        const data = trialResp.data;
        const processedAmounts = {
          totalAmountInYuan: convertFenToYuan(data?.totalAmount),
          totalPrincipalInYuan: convertFenToYuan(data?.totalPrincipal),
          totalInterestInYuan: convertFenToYuan(data?.totalInterest),
        };
        this.firstTrialInfo = this.trialInfo = { ...data, ...processedAmounts };
      }
    },

    async trial() {
      const res = await repayTrial({
        outOrderNo: this.param.outOrderNo,
        repayAmountStr: Big(Number(this.repayAmountStr)).times(100),
        budgetType: this.param.budgetType,
      });
      if (res?.code !== 0) {
        const data= res.data;
        const processedAmounts = {
          totalAmountInYuan: convertFenToYuan(data?.totalAmount),
          totalPrincipalInYuan: convertFenToYuan(data?.totalPrincipal),
          totalInterestInYuan: convertFenToYuan(data?.totalInterest),
        };
        this.trialInfo = { ...data, ...processedAmounts };
      }
    },
  },
});
