<script setup>
import { computed, onMounted, onServerPrefetch, ref } from "vue";
import Big from "big.js";
import lodash from "lodash";
import { del, getStore } from "../../../helpers/storage";
import { alipay, back } from "../../../helpers/native-bridge";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { amountNumberFormat, initStore } from "../../../helpers/utils";
import useStore from "./store";
import { repaySubmit } from "../../../api/repay.js";

const { store, data } = initStore(useStore);

const isRepayAll = ref(false);
const amountInputError = ref("");
const trialLoading = ref(false);

onServerPrefetch(store.initial);

const subtitle = computed(() => {
  if (store.param.isDelay === "true") {
    if (store.param.isOverdueAndOnNextPeriodRepayDay === "false") {
      return `已逾期${store.param.delayDays}天，逾期欠款￥${amountNumberFormat(store.totalAmountInYuan)})`;
    }
    return `已逾期${store.param.delayDays}天，应￥${amountNumberFormat(store.totalAmountInYuan)})`;
  }
  return `截至今日剩余待还金额¥${amountNumberFormat(store.totalAmountInYuan)}（含利息¥${amountNumberFormat(store.totalInterestInYuan)}）`;
});

const processAmountTrial = async () => {
  amountInputError.value = "";
  isRepayAll.value = false;
  const currentAmount = store.repayAmount;
  if (currentAmount === "") {
    trialLoading.value = false;
    return;
  }
  if (currentAmount.startsWith(".")) {
    amountInputError.value = "金额有误";
    trialLoading.value = false;
    return;
  }
  const parsedAmount = Number(currentAmount);
  if (
    Number.isNaN(parsedAmount) ||
    parsedAmount <= 0 ||
    !/^(?:[1-9]\d*|0)(?:\.\d+|\.)?$/.test(currentAmount)
  ) {
    amountInputError.value = "金额有误";
    trialLoading.value = false;
    return;
  }

  if (parsedAmount > store.totalAmountInYuan) {
    amountInputError.value = "还款金额不能大于待还金额";
    trialLoading.value = false;
    return;
  }

  isRepayAll.value = currentAmount === store.totalAmountInYuan;
  try {
    await store.trial();
  } catch (error) {
    console.error("试算失败:", error);
    amountInputError.value = "试算服务异常";
  } finally {
    trialLoading.value = false;
  }
};
const debouncedProcessAmountTrial = lodash.debounce(processAmountTrial, 1000);
const handleAmountInput = () => {
  trialLoading.value = true;
  amountInputError.value = "";
  debouncedProcessAmountTrial();
};

const checkStrLong = (value) =>
  value
    .replace(/^(\\-)*(\d+)\.(\d\d).*$/, "$1$2.$3")
    .replace(/-*/g, "")
    .replace(/\+*/g, "")
    .replace(/\**/g, "");

const repayAll = async () => {
  trialLoading.value = true;
  isRepayAll.value = true;
  amountInputError.value = "";
  store.repayAmount = store.totalAmountInYuan;
  await store.trial();
  trialLoading.value = false;
};

const startWeb = ref("hidden");
onMounted(async () => {
  if (getStore(`U_REPAY_FAIL_${data.value.param.userId}`)) {
    del(`U_REPAY_FAIL_${data.value.param.userId}`);
  }
  del(`U_REPAYMENT_SMS_REPAYNO_${data.value.param.userId}`);
  setTimeout(() => {
    startWeb.value = "visible";
  });
});
const confirmButtonDisabled = computed(() => {
  const amountStr = store.repayAmount;
  let amountBig;
  if (!amountStr || amountStr.trim() === "" || Number.isNaN(Number(amountStr))) {
    return true;
  }
  try {
    amountBig = new Big(amountStr);
    if (amountBig.lte(0)) {
      return true;
    }
  } catch (e) {
    return true;
  }
  return amountInputError.value !== "" || trialLoading.value;
});

const gotoAlipay = async () => {
  const params = {
    totalAmount: Big(Number(store.repayAmountStr)).times(100),
    budgetType: store.param.budgetType,
    budgetAmount: store.firstTrialInfo.totalAmount,
    budgetInterest: store.firstTrialInfo.totalInterest,
    budgetPrincipal: store.firstTrialInfo.totalPrincipal,
    budgetPenalty: store.firstTrialInfo.totalPenalty,
  }
  const repayRes = await repaySubmit(params);

  if (repayRes?.code !== 0) {
    return;
  }

  const repayData = repayRes.data;

  alipay(repayData.orderInfo);
};
</script>

<template>
  <div class="bg" :style="{ visibility: startWeb }">
    <div>
      <hnr-nav-bar transparent="true" class="nav-padding-top" :title="'还款明细'">
        <template #left>
          <icsvg-public-back-filled @click="back"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
    </div>

    <div
      class="sub-title"
      :style="{ color: store.param.isDelay === 'true' ? 'var(--hnr-color-error)' : null }"
    >
      {{ subtitle }}
    </div>

    <div class="trial-content">
      <div class="account-input-field">
        <div class="rmb-signal">￥</div>
        <hnr-field
          v-model="store.repayAmountStr"
          type="number"
          :placeholder="'待还￥' + amountNumberFormat(store.totalAmountInYuan)"
          :formatter="checkStrLong"
          :with-margin="false"
          @update:model-value="handleAmountInput"
        >
          <template #button>
            <hnr-button
              :disabled="isRepayAll"
              style="background-color: transparent; padding-right: 0rem; min-width: 0"
              type="default"
              @click="repayAll"
              >{{ isRepayAll ? "已还全部" : "还全部" }}</hnr-button
            >
          </template>
        </hnr-field>
      </div>
      <div class="divide-line"></div>
      <div v-show="amountInputError" class="input-error">{{ amountInputError }}</div>
      <div
        v-show="
          !amountInputError &&
          store.repayAmountStr &&
          !Number.isNaN(Number(store.repayAmountStr)) &&
          Big(store.repayAmountStr || 0) > 0
        "
      >
        <div class="cellLine">
          <div class="cellLeft">还本金</div>
          <div class="cellRight">
            {{ trialLoading ? "计算中" : "￥" + amountNumberFormat(store.totalPrincipalInYuan) }}
          </div>
        </div>
        <div class="cellLine">
          <div class="cellLeft">利息</div>
          <div class="cellRight">
            {{ trialLoading ? "计算中" : "￥" + amountNumberFormat(store.totalInterestInYuan) }}
          </div>
        </div>
      </div>
    </div>

    <div class="footer">
      <hnr-button
        type="primary"
        class="content-bottom-btn"
        standard-width="true"
        :disabled="confirmButtonDisabled"
        @click="gotoAlipay"
      >
        支付宝还款{{
          store.repayAmountStr && "（￥" + amountNumberFormat(new Big(store.repayAmountStr)) + "）"
        }}
      </hnr-button>
    </div>
  </div>
</template>

<style scoped>
.bg {
  user-select: none;
  display: flex;
  flex-direction: column;
  position: fixed;
  background: var(--hnr-color-background-cardview);
  height: 100%;
  width: 100%;
}

.sub-title {
  margin: 0 var(--dp24);
  color: var(--hnr-text-color-primary);
  font: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}

.trial-content {
  background-color: var(--hnr-color-list-card-background);
  border-radius: var(--hnr-card-corner-radius);
  margin: var(--hnr-elements-margin-vertical-M2);
  padding-bottom: var(--hnr-elements-margin-vertical-M2);
}

.account-input-field {
  margin: 0 var(--hnr-elements-margin-vertical-M2);
  display: flex;
  align-items: center;
}

.rmb-signal {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
  margin-right: var(--hnr-elements-margin-horizontal-M);
}

:deep(.hnr-field__input-area:before) {
  width: 0;
}

:deep(.hnr-field__control) {
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-regular);
}
:deep(.hnr-field__control::-webkit-input-placeholder) {
  /* placeholder颜色  */
  color: var(--hnr-text-color-tertiary);
  /* placeholder字体大小  */
  font-size: var(--hnr-font-weight-regular);
  /* placeholder位置  */
  /* text-align: right; */
}

:deep(.hnr-field--withMargin) {
  width: 100%;
  margin: 0;
}

.divide-line {
  height: var(--dp1);
  background-color: var(--hnr-list-divider);
  margin: 0 var(--hnr-elements-margin-vertical-M2);
}

.input-error {
  margin: 0 var(--hnr-elements-margin-vertical-M2);
  color: var(--hnr-color-error);
  font-size: calc(var(--hnr-body-3) / var(--hnr-large-font-rate));
  text-align: left;
}

.cellLine {
  width: 100%;
  position: relative;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  justify-content: space-between;
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-start);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}

.cellLeft {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.cellRight {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}

.footer {
  display: flex;
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 99;
  justify-content: center;
}

.content-bottom-btn {
  max-width: none !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 var(--hnr-default-padding-bottom-fixed) 0;
}
</style>
