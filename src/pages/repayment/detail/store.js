import { defineStore } from "pinia";
import { request } from "../../../helpers/utils";

export default defineStore("repayment/detail", {
  state: () => ({
    // 页面初始数据
    isShowMore: false, // 是否展示更多信息
    isShowContract: false, // 是否显示合同弹窗
    isShowTips: false,
    param: {},
    planConpleteIndex: -1, // 已完成计划列表的下标
    isOverdue: false, // 是否逾期
    overdueDays: 0, // 逾期天数
    overdueDebts: 0, // 逾期欠款
    repaidAmount: 0, // 已还金额
    waitRepayAmount: 0, // 待还金额
    loanDate: "", // 借款日期
    loanPrincipal: 0, // 借款本金
    totalInstallmentAmount: 0, // 分期总额
    status: 0, // 借款状态
    installmentInterest: 0, // 利息
    interestDiscount: 0, // 优惠金额
    lateCharge: 0, // 逾期罚息
    loanOrderNumber: 0, // 借款订单号
    totalInstallments: 0, // 分期总数
    repayMethodName: "", // 还款方式text
    dailyInterestRate: "", // 实际日利率
    serviceProvider: "", // 服务提供商
    contributor: "", // 出资方
    receivingBank: "", // 收款银行
    bindCardNo: "", // 收款银行卡
    agreementList: [], // 协议列表
    repayPlanTerms: [], // 还款计划列表
    isShowContractDetailPopup: false, // 是否显示协议详情弹窗
    isShowContractDetailPopupUrl: false, // 是否显示协议详情弹窗url
    contractName: "", // 协议名称
    contractHtml: "", // 协议html
    contractUrl: "", // 协议url
    isLoading: true, // 是否在请求接口
    apr: 0, // 年利率
    supplierId: 0,
    supplierName: "", // 服务提供商
    prePenalty: "", // 违约金
    pdfKey: 1,
    serviceFee: 0, // 服务费
    interAndFee: 0, // 息费
  }),
  actions: {
    async initial() {
      try {
        const res = await request(
          "/loan/api/loan/detail",
          { outOrderNo: this.param.loanOrderId },
          { mock: false },
        );
        this.isLoading = false;
        if (res.code !== 0) {
          this.errorCode = res.code;
          return;
        }
        const { data } = res;
        // 已还金额
        this.repaidAmount = data?.paidAmount;
        // 借款日期
        this.loanDate = data?.applyDate;
        // 借款本金
        this.loanPrincipal = data?.loanAmount;
        // 借款状态
        this.status = data?.status;
        // 借款订单号
        this.loanOrderNumber = data?.outOrderNo;
        // 分期总数
        this.totalInstallments = data?.totalTerm;
        // 还款方式text
        this.repayMethodName = data?.repayMethodName;
        // 年利率
        this.apr = data?.apr;
        // 实际日利率
        this.dailyInterestRate = data?.dayRate;
        // 服务提供商
        this.serviceProvider = data?.supplier;
        // 出资方
        this.contributor = data?.institutionNames;
        // 收款银行
        this.receivingBank = data?.bindBankName;
        // 收款银行卡
        this.bindCardNo = data?.bindCardNo;
        // 违约金
        this.prePenalty = data?.prePenalty;
        // 协议列表
        this.agreementList = data?.contractList?.map((item) => {
          return {
            ...item,
            isLoading: false,
          };
        });
        // 还款计划列表
        this.repayPlanTerms = data?.repayPlanTerms.map((item) => {
          return {
            ...item,
            // 判断服务费、罚息、违约金是否一行展示
            ifOneRowShow: !(
              (item.termPrinPenalty + item.termInterPenalty || item.termPenalty) &&
              item.paidTermViolateFee &&
              parseFloat(item?.termServiceFee)
            ),
          };
        });
        // 是否逾期
        this.isOverdue = this.repayPlanTerms.some((item) => item.overdue === true);
        // 逾期天数
        const overdueIndex = this.repayPlanTerms.findIndex((item) => item.overdue === true);
        this.overdueDays = overdueIndex === -1 ? 0 : this.repayPlanTerms[overdueIndex].overdueDays;

        this.repayPlanTerms.forEach((item) => {
          // 逾期欠款
          if (overdueIndex !== -1) {
            if (item.overdue) {
              this.overdueDebts += item.overdueAmt;
            }
          }
          // 待还总额
          if (item.termStatus !== 1 && item.termReductionAmount) {
            this.waitRepayAmount +=
              item.payableTermAmount - item.termReductionAmount + item.paidTermReductionAmount;
          } else {
            this.waitRepayAmount += item.payableTermAmount;
          }

          // 应还总额
          this.totalInstallmentAmount += item.termAmount - item.termReductionAmount;
          // 利息
          this.installmentInterest += item.termInterest;
          // 服务费
          this.serviceFee += item.termServiceFee;
          // 息费
          this.interAndFee += item.termInterAndFee;

          // 优惠金额
          this.interestDiscount += item.termReductionAmount;
          // 逾期罚息
          this.lateCharge +=
            item.termPrinPenalty + item.termInterPenalty
              ? item.termPrinPenalty + item.termInterPenalty
              : item.termPenalty || 0;
        });

        // 已完成计划列表的最后的下标
        this.planConpleteIndex = this.repayPlanTerms.findLastIndex((item) => item.termStatus === 1);
        const isEnd = this.repayPlanTerms.every((item) => item.termStatus === 1);
        if (isEnd) {
          this.planConpleteIndex = -1;
        }
      } catch (error) {
        throw new Error(error);
      }
    },
    getOrderStatus() {
      if (this.status === 1) return "申请中";
      if (this.status === 2) return "未结清";
      if (this.status === 3) return "已逾期";
      if (this.status === 4) return "已结清";
      return null;
    },
  },
});
