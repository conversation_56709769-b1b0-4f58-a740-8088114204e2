<script setup>
import { onServerPrefetch, computed, onMounted, ref } from "vue";
import lodash from "lodash";
import Big from "big.js";
import { showToast, showDialog } from "@hihonor/hnr/dist/hnr.es.min";
import useStore from "./store";
import { initStore, amountNumberFormat, request } from "../../../helpers/utils";
import { back, goto, regNativeEvent, report } from "../../../helpers/native-bridge";
import { getStore, del, setWithExactExpireUnit } from "../../../helpers/storage";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { getCurrentCpInfo } from "../../../helpers/configUtils";
import { repayStatusQuery, transferRepay } from "../../../api/repay";
import { repayStatusMap } from "../../../api/typedef/repay";

const { store, data } = initStore(useStore);
const showamt = computed(
  () =>
    Big(data.value.wholeamt || 0)
      .div(100)
      .toFixed(2),
  21,
);
const realamt = computed(() =>
  data.value.termloading
    ? "（计算中）"
    : `（￥${amountNumberFormat(
        Big(data.value.trialinfo.totalAmount || 0)
          .div(100)
          .toFixed(2),
      )}）`,
);
onServerPrefetch(store.initial);

const isNoInput = ref(false);
const onInput = lodash.debounce(() => {
  data.value.clickflag = false;
  if (data.value.repayamt === "") {
    data.value.amterror = "";
    data.value.termloading = false;
    isNoInput.value = true;
    return;
  }
  isNoInput.value = false;
  if (
    data.value.repayamt - 0 <= 0 ||
    data.value.repayamt.startsWith(".") ||
    !/^(?:[1-9]\d*|0)(?:\.\d+|\.)?$/.test(data.value.repayamt)
  ) {
    data.value.amterror = "金额有误";
    data.value.termloading = false;
    return;
  }
  if (data.value.repayamt - 0 > showamt.value - 0) {
    data.value.amterror = "还款金额不能大于待还金额";
    data.value.termloading = false;
  } else {
    data.value.clickflag = data.value.repayamt === showamt.value;
    data.value.amterror = "";
    store.prepayTrial();
  }
}, 1000);

const showViolateInterestBubble = ref(false);
const showViolateFeeBubble3 = ref(false);
const showViolateFeeBubble1 = ref(false);
const showViolateFeeBubble2 = ref(false);
const showViolateFeeBubble4 = ref(false);
const showViolateFeeBubble5 = ref(false);
function custinput() {
  data.value.termloading = true;
  return onInput();
}

function bankinfo(param) {
  data.value.bankcardid = param.bankCardId;
  data.value.bankCardNo = param.bankCardNo;
  store.isReservePhoneError = false;
}

regNativeEvent("onResume", async () => {
  if (getStore(`U_REPAY_FAIL_${data.value.param.userId}`)) {
    del(`U_REPAY_FAIL_${data.value.param.userId}`);
    data.value.btnloading = false;
  }
  del(`U_REPAYMENT_SMS_REPAYNO_${data.value.param.userId}`);

  const repayNo = sessionStorage.getItem("repayNo");
  if (repayNo) {
    const bankTransferStartTime = sessionStorage.getItem("bankTransferStartTime");
    sessionStorage.removeItem("bankTransferStartTime");
    sessionStorage.removeItem("repayNo");
    const diff = Date.now() - Number(bankTransferStartTime);
    if (diff > 10000) {
      store.showQueryRepayStatusDialog = true;
      const repayStatusRes = await repayStatusQuery(repayNo);
      store.showQueryRepayStatusDialog = false;
      if (repayStatusRes?.code === 0) {
        const { repayStatus } = repayStatusRes.data;
        if (repayStatus !== repayStatusMap.Repaying) {
          goto(
            `/wallet-loan-web/pages/repayment/record?repayNo=${repayNo}&repayResult=${repayStatus}`,
            false,
          );
        }
      }
    }
  }
});
regNativeEvent("onNetChange", () => {
  if (window?.navigator?.onLine) {
    custinput();
  }
});

const startWeb = ref("hidden");
onMounted(async () => {
  if (getStore(`U_REPAY_FAIL_${data.value.param.userId}`)) {
    del(`U_REPAY_FAIL_${data.value.param.userId}`);
  }
  del(`U_REPAYMENT_SMS_REPAYNO_${data.value.param.userId}`);
  setTimeout(() => {
    startWeb.value = "visible";
  });

  const res = await getCurrentCpInfo();
  // 违约利息说明
  data.value.repayViolateInterestExplanation = res?.repayViolateInterestExplanation;

  // 违约金说明
  data.value.repayViolateFeeExplanation = res?.repayViolateFeeExplanation;
  data.value.interestFeeExplanation = res?.interestFeeExplanation;
  report("wallet_page_view", {
    page_name: "repayment_trial_page",
  });
});

function repayall() {
  data.value.termloading = true;
  data.value.clickflag = true;
  data.value.amterror = "";
  data.value.repayamt = Big(data.value.wholeamt || 0)
    .div(100)
    .toFixed(2);
  store.repayall();
}

const comfireButtonDisabled = computed(() => {
  if (data.value.param.isdelay !== "true") {
    if (data.value.isall) {
      return (
        data.value.amterror !== "" ||
        !data.value.bankcardid ||
        data.value.termloading ||
        data.value.repayAmount
      );
    }
  }
  return (
    !(
      data.value.repayamt &&
      !Number.isNaN(Number(data.value.repayamt)) &&
      Big(data.value.repayamt || 0) > 0
    ) ||
    data.value.amterror !== "" ||
    !data.value.bankcardid ||
    data.value.termloading ||
    data.value.repayAmount
  );
});

async function submit() {
  setWithExactExpireUnit(`U_REPAYTERMS_${data.value.param.userId}`, data.value.repayTerms, 10, "M");
  if (!data.value.bankcardid) {
    showToast({
      message: "银行卡有误",
      position: "bottom",
    });
    return;
  }
  if (
    (data.value.repayamt - 0 <= 0 || data.value.trialinfo.totalAmount - 0 <= 0) &&
    !data.value.isall
  ) {
    data.value.amterror = "金额有误";
    return;
  }
  if (data.value.repayamt - 0 > showamt.value - 0 && !data.value.isall) {
    data.value.amterror = "还款金额不能大于待还金额";
    return;
  }
  setTimeout(() => {
    let partPay = "";
    if (store.repayamt) {
      partPay = Big(store.repayamt).eq(Big(store.wholeamt || 0).div(100)) ? "（全部）" : "(部分)";
    }
    report("wallet_page_click", {
      click_name: "repayment_trial_repay_click",
      repayType: `${store.param.repayType}${partPay}`,
    });
  });
  data.value.btnloading = true;
  try {
    const resp = await request("/loan/api/repay/v2/sendVerifyCode", {
      totalAmount: data.value.trialinfo.totalAmount,
      bankCardId: data.value.bankcardid,
      outOrderNo: data.value.param.outOrderNo,
    });
    setTimeout(async () => {
      await request("/loan/api/user/operLog", {
        orderNo: data.value.param.outOrderNo,
        operType: 302,
        operResult: resp.code === 0 ? 1 : 2,
        supplier: 1,
        operTime: new Date().getTime(),
      });
    }, 0);
    if (resp.code === 0) {
      if (resp?.data.status === 173402) {
        data.value.btnloading = false;
        showToast({
          message: "还款操作频繁，请稍后再试",
          position: "bottom",
        });
        return;
      }
      if (resp.data.status === 0) {
        if (data.value.isall) {
          // 全部结清
          goto(
            `/wallet-loan-web/pages/repayment/sms?repayPart=${
              data.value.repayamt !==
              Big(data.value.wholeamt || 0)
                .div(100)
                .toFixed(2)
            }&` +
              `totalAmount=${data.value.trialinfo.totalAmount}&` +
              `bankCardId=${data.value.bankcardid}&` +
              `bankCardNo=${data.value.bankCardNo}&` +
              `outOrderNo=${data.value.param.outOrderNo}&` +
              `repayType=${data.value.param.isdelay === "true" ? 2 : 1}&` +
              `needResign=${resp.data.needResign}&` +
              `couponNo=${data.value.couponNo}&` +
              `reductionAmount=${data.value.discount}&` +
              `serialNo=${resp.data.serialNo}`,
          );
        } else {
          // 随借随还
          goto(
            "/wallet-loan-web/pages/repayment/sms?" +
              `repayPart=${
                data.value.repayamt !==
                Big(data.value.wholeamt || 0)
                  .div(100)
                  .toFixed(2)
              }&` +
              `totalAmount=${data.value.trialinfo.totalAmount}&` +
              `bankCardId=${data.value.bankcardid}&` +
              `outOrderNo=${data.value.param.outOrderNo}&` +
              `bankCardNo=${data.value.bankCardNo}&` +
              `repayType=${data.value.param.isdelay === "true" ? 2 : 1}` +
              `&needResign=${resp.data.needResign}&` +
              `couponNo=${data.value.couponNo}&` +
              `reductionAmount=${data.value.discount}&` +
              `serialNo=${resp.data.serialNo}`,
          );
        }
      }
    } else {
      if (resp?.code === 171911) {
        showDialog({
          message: resp.message,
          messageAlign: "left",
          confirmButtonType: "normal",
          confirmButtonText: "知道了",
          showFilter: true,
          beforeClose: () => {
            data.value.isReservePhoneError = true;
            return true;
          },
        });
      } else {
        showToast({
          message: resp?.message || "还款操作频繁，请稍后再试",
          position: "bottom",
        });
      }
      data.value.btnloading = false;
    }
  } catch (e) {
    data.value.btnloading = false;
  }
}

const checkstrlong = (value) =>
  value
    .replace(/^(\\-)*(\d+)\.(\d\d).*$/, "$1$2.$3")
    .replace(/-*/g, "")
    .replace(/\+*/g, "")
    .replace(/\**/g, "");

// 是否展示银行转账卡片
const showBankTransferCard = computed(() => {
  return store.param.sdkVersionCode >= ********* && store?.supplierInfo?.supportTransferRepay === 1;
});

const goToTransferRepayPage = async () => {
  // 调用服务端接口获取订单号和url
  const param = {
    totalAmount: data.value.trialinfo.totalAmount,
    repayType: data.value.param.isdelay === "true" ? 2 : 1,
    repayItemList: [
      {
        couponNo: data.value.couponNo,
        reductionAmount: data.value.discount,
        outOrderNo: data.value.param.outOrderNo,
        repayAmount: data.value.trialinfo.totalAmount,
        repayTerms: data.value.repayTerms,
      },
    ],
  };
  const transferRepayRes = await transferRepay(param);

  if (transferRepayRes?.code === 0) {
    sessionStorage.setItem("bankTransferStartTime", String(Date.now()));
    sessionStorage.setItem("repayNo", transferRepayRes.data.repayNo);
    await back();
    goto(transferRepayRes.data.repayUrl, true);
  }
};
</script>

<template>
  <div class="bg" :style="{ visibility: startWeb }">
    <div>
      <hnr-nav-bar
        v-show="data.param.isdelay !== 'true'"
        transparent="true"
        class="nav-padding-top"
        :title="data.isall ? '全部结清' : '提前还款'"
      >
        <template #left>
          <icsvg-public-back-filled @click="back"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
      <hnr-nav-bar
        v-show="data.param.isdelay === 'true'"
        transparent="true"
        class="nav-padding-top"
        title="还部分逾期"
      >
        <template #left>
          <icsvg-public-back-filled @click="back"></icsvg-public-back-filled>
        </template>
      </hnr-nav-bar>
    </div>
    <!-- 延期 -->
    <div v-show="data.param.isdelay === 'true'">
      <div class="describe" style="color: var(--hnr-color-error)">
        已逾期{{ data.param.delaydays }}天，逾期总应还￥{{ amountNumberFormat(showamt) }}(含欠款￥{{
          amountNumberFormat(new Big(data.param.repayAmount).div(100))
        }})
      </div>
      <div class="accountNum">
        <div class="title1">{{ "还部分逾期" }}</div>
        <div class="content">
          <div class="rmb">￥</div>
          <hnr-field
            v-model="data.repayamt"
            :placeholder="'待还￥' + amountNumberFormat(showamt)"
            :formatter="checkstrlong"
            :with-margin="false"
            class="fileds"
            @update:model-value="custinput"
          >
            <template #button>
              <hnr-button
                :disabled="data.clickflag"
                type="default"
                style="background-color: transparent; padding-right: 0rem; min-width: 0"
                @click="repayall"
                >{{ data.clickflag ? "已还全部" : "还全部" }}</hnr-button
              >
            </template>
          </hnr-field>
        </div>
        <div class="line"></div>
        <div v-show="data.amterror" class="errmsg">{{ data.amterror }}</div>
        <div
          v-show="
            !data.amterror &&
            data.repayamt &&
            !Number.isNaN(Number(data.repayamt)) &&
            Big(data.repayamt || 0) > 0
          "
        >
          <div class="cellLine">
            <div class="cellLeft">还本金</div>
            <div class="cellRight">
              {{
                data.termloading
                  ? "计算中"
                  : "￥" + amountNumberFormat(data.trialinfo?.totalPrincipal)
              }}
            </div>
          </div>
          <div v-show="parseFloat(data.trialinfo?.totalInterAndFee)" class="cellLine">
            <div class="cellLeft" style="display: flex; align-items: center">
              息费
              <hnr-bubble-tip
                v-model:show="showViolateFeeBubble5"
                :message="data.interestFeeExplanation"
                placement="bottom"
                class="violateFee-bubble-tip"
              >
                <template #reference>
                  <hnr-icon>
                    <img src="/loan/helps.svg" alt class="imgs" />
                  </hnr-icon>
                </template>
              </hnr-bubble-tip>
            </div>
            <div class="cellRight">
              {{
                data.termloading
                  ? "计算中"
                  : "￥" + amountNumberFormat(data.trialinfo?.totalInterAndFee)
              }}
            </div>
          </div>
          <div v-show="!parseFloat(data.trialinfo?.totalInterAndFee)" class="cellLine">
            <div class="cellLeft">利息</div>
            <div class="cellRight">
              {{
                data.termloading
                  ? "计算中"
                  : "￥" + amountNumberFormat(data.trialinfo?.totalInterest)
              }}
            </div>
          </div>
          <div
            v-show="data.trialinfo?.totalPenalty !== '0.00' && data.trialinfo?.totalPenalty !== 0"
            class="cellLine"
          >
            <div class="cellLeft">逾期罚息</div>
            <div class="cellRight">
              {{
                data.termloading
                  ? "计算中"
                  : "￥" + amountNumberFormat(data.trialinfo?.totalPenalty)
              }}
            </div>
          </div>
          <div
            v-show="
              parseFloat(data.trialinfo?.totalServiceFee) &&
              !parseFloat(data.trialinfo?.totalInterAndFee)
            "
            class="cellLine"
          >
            <div class="cellLeft" style="display: flex; align-items: center">
              服务费
              <hnr-bubble-tip
                v-model:show="showViolateFeeBubble1"
                :message="data.repayViolateFeeExplanation"
                placement="bottom"
              >
                <template #reference>
                  <hnr-icon>
                    <img src="/loan/helps.svg" alt class="imgs" />
                  </hnr-icon>
                </template>
              </hnr-bubble-tip>
            </div>
            <div class="cellRight">
              {{
                data.termloading
                  ? "计算中"
                  : "￥" + amountNumberFormat(data.trialinfo?.totalServiceFee)
              }}
            </div>
          </div>
          <div v-show="!Big(data.trialinfo?.totalDiscount || 0).eq(0)" class="cellLine">
            <div class="cellLeft">优惠券</div>
            <div class="cellRight">
              {{
                data.termloading
                  ? "计算中"
                  : "-￥" + amountNumberFormat(data.trialinfo?.totalDiscount)
              }}
            </div>
          </div>
        </div>
        <myBank
          class="container1"
          title="还款银行卡"
          label="还款银行卡"
          :escapeinit="!data.param.isdelay === 'true'"
          :apply-no="data.param?.outOrderNo"
          agree="false"
          :is-reserve-phone-error="data.isReservePhoneError"
          :show-bank-transfer-card="showBankTransferCard"
          @bankinfo="bankinfo"
          @go-to-transfer-repay-page="goToTransferRepayPage"
        ></myBank>
        <hnr-divider
          v-if="data.trialinfo?.reductionAmountDesc"
          style="padding: 0 var(--dp12); width: auto"
          line
        />
        <div
          v-if="data.trialinfo?.reductionAmountDesc"
          style="
            border-radius: var(--hnr-default-corner-radius-xs);
            padding: var(--dp12) var(--dp12) var(--dp16) var(--dp12);
            font-size: var(--hnr-caption);
            color: var(--hnr-text-color-tertiary);
          "
          v-html="data.trialinfo?.reductionAmountDesc"
        ></div>
      </div>
    </div>
    <!-- 未延期 -->
    <div v-show="data.param.isdelay !== 'true'">
      <div v-show="data.isall">
        <!-- 固定期限即全部结清 -->
        <hnr-cell-group class="container" inset>
          <div>
            <div v-if="parseFloat(data.trialinfo?.totalInterAndFee)" class="container1">
              <span class="infotitle1">提前结清借款，待还总额</span>
              <span class="infotitle2">￥{{ amountNumberFormat(showamt) }}</span>
              <span class="infotitle3"
                >(含息费￥{{
                  amountNumberFormat(data.trialinfo?.totalInterAndFee)
                }}，提前还款违约金￥{{ amountNumberFormat(data.trialinfo?.totalViolateFee) }})</span
              >
            </div>
            <div v-if="!parseFloat(data.trialinfo?.totalInterAndFee)" class="container1">
              <span class="infotitle1">提前结清借款，待还总额</span>
              <span class="infotitle2">￥{{ amountNumberFormat(showamt) }}</span>
              <span class="infotitle3"
                >(含利息￥{{
                  amountNumberFormat(data.trialinfo?.totalInterest)
                }}，提前还款违约金￥{{ amountNumberFormat(data.trialinfo?.totalViolateFee) }})</span
              >
            </div>
            <hnr-divider line />
            <div class="cellLine">
              <div class="cellLeft">待还本金</div>
              <div class="cellRight">
                {{
                  data.termloading
                    ? "计算中"
                    : "￥" + amountNumberFormat(data.trialinfo?.totalPrincipal)
                }}
              </div>
            </div>
            <div v-if="parseFloat(data.trialinfo?.totalInterAndFee)" class="cellLine">
              <div class="cellLeft" style="display: flex; align-items: center">
                待还息费
                <hnr-bubble-tip
                  v-model:show="showViolateFeeBubble4"
                  :message="data.interestFeeExplanation"
                  placement="bottom"
                  class="violateInterest-bubble-tip"
                >
                  <template #reference>
                    <hnr-icon>
                      <img src="/loan/helps.svg" alt class="imgs" />
                    </hnr-icon>
                  </template>
                </hnr-bubble-tip>
              </div>
              <div class="cellRight">
                {{
                  data.termloading
                    ? "计算中"
                    : "￥" + amountNumberFormat(data.trialinfo?.totalInterAndFee)
                }}
              </div>
            </div>
            <div v-if="!parseFloat(data.trialinfo?.totalInterAndFee)" class="cellLine">
              <div class="cellLeft" style="display: flex; align-items: center">
                待还利息
                <hnr-bubble-tip
                  v-show="
                    data.repayViolateInterestExplanation && store.loanOrderDetail?.repayMethod === 4
                  "
                  v-model:show="showViolateInterestBubble"
                  :message="data.repayViolateInterestExplanation"
                  placement="bottom"
                  class="violateInterest-bubble-tip"
                >
                  <template #reference>
                    <hnr-icon>
                      <img src="/loan/helps.svg" alt class="imgs" />
                    </hnr-icon>
                  </template>
                </hnr-bubble-tip>
              </div>
              <div class="cellRight">
                {{
                  data.termloading
                    ? "计算中"
                    : "￥" + amountNumberFormat(data.trialinfo?.totalInterest)
                }}
              </div>
            </div>
            <div class="cellLine">
              <div class="cellLeft">剩余期数</div>
              <div class="cellRight">{{ data.termloading ? "计算中" : data.param?.period }}</div>
            </div>
            <div
              v-if="
                parseFloat(data.trialinfo?.totalServiceFee) &&
                !parseFloat(data.trialinfo?.totalInterAndFee)
              "
              class="cellLine"
            >
              <div class="cellLeft" style="display: flex; align-items: center">
                服务费
                <hnr-bubble-tip
                  v-model:show="showViolateFeeBubble2"
                  :message="data.repayViolateFeeExplanation"
                  placement="bottom"
                  class="violateFee-bubble-tip"
                >
                  <template #reference>
                    <hnr-icon>
                      <img src="/loan/helps.svg" alt class="imgs" />
                    </hnr-icon>
                  </template>
                </hnr-bubble-tip>
              </div>
              <div class="cellRight" style="display: flex; align-items: center">
                {{
                  data.termloading
                    ? "计算中"
                    : "￥" + amountNumberFormat(data.trialinfo?.totalServiceFee)
                }}
              </div>
            </div>
            <div v-show="!Big(data.trialinfo?.totalDiscount || 0).eq(0)" class="cellLine">
              <div class="cellLeft">优惠金额</div>
              <div class="cellRight">
                {{
                  data.termloading
                    ? "计算中"
                    : "-￥" + amountNumberFormat(data.trialinfo?.totalDiscount)
                }}
              </div>
            </div>
            <div v-if="data.trialinfo?.totalViolateFee !== '0.00'" class="cellLine">
              <div class="cellLeft" style="display: flex; align-items: center">
                违约金
                <hnr-bubble-tip
                  v-model:show="showViolateFeeBubble3"
                  :message="data.repayViolateFeeExplanation"
                  placement="bottom"
                  class="violateFee-bubble-tip"
                >
                  <template #reference>
                    <hnr-icon>
                      <img src="/loan/helps.svg" alt class="imgs" />
                    </hnr-icon>
                  </template>
                </hnr-bubble-tip>
              </div>
              <div class="cellRight" style="display: flex; align-items: center">
                {{
                  data.termloading
                    ? "计算中"
                    : "￥" + amountNumberFormat(data.trialinfo?.totalViolateFee)
                }}
              </div>
            </div>
            <myBank
              class="container1"
              title="还款银行卡"
              label="还款银行卡"
              :escapeinit="data.param.isdelay === 'true' && !data.isall"
              :apply-no="data.param?.outOrderNo"
              agree="false"
              :is-reserve-phone-error="data.isReservePhoneError"
              :show-bank-transfer-card="showBankTransferCard"
              @bankinfo="bankinfo"
              @go-to-transfer-repay-page="goToTransferRepayPage"
            ></myBank>
            <hnr-divider
              v-if="data.trialinfo?.reductionAmountDesc"
              style="padding: 0 var(--dp12); width: auto"
              line
            />
            <div
              v-if="data.trialinfo?.reductionAmountDesc"
              style="
                border-radius: var(--hnr-default-corner-radius-xs);
                padding: var(--dp12) var(--dp12) var(--dp16) var(--dp12);
                font-size: var(--hnr-caption);
                color: var(--hnr-text-color-tertiary);
              "
              v-html="data.trialinfo?.reductionAmountDesc"
            ></div>
          </div>
        </hnr-cell-group>
      </div>
      <div v-show="!data.isall">
        <!-- 随借随还即提前还款 -->
        <div class="describe">
          提前还款，截止今日剩余待还金额￥{{ amountNumberFormat(showamt) }}
          <span v-if="isNoInput"
            >（含分期利息￥{{ amountNumberFormat(data.initTotalInterest) }}）</span
          >
          <span v-else
            >（含分期利息￥{{ amountNumberFormat(data.trialinfo?.totalInterest) }}）</span
          >
        </div>
        <div class="accountNum">
          <div class="title1">提前还款</div>
          <div class="content">
            <div class="rmb">￥</div>
            <hnr-field
              v-model="data.repayamt"
              type="number"
              :placeholder="'待还￥' + amountNumberFormat(showamt)"
              :formatter="checkstrlong"
              :with-margin="false"
              class="fileds"
              @update:model-value="custinput"
            >
              <template #button>
                <hnr-button
                  :disabled="data.clickflag"
                  style="background-color: transparent; padding-right: 0rem; min-width: 0"
                  type="default"
                  @click="repayall"
                  >{{ data.clickflag ? "已还全部" : "还全部" }}</hnr-button
                >
              </template>
            </hnr-field>
          </div>
          <div class="line"></div>
          <div v-show="data.amterror" class="errmsg">{{ data.amterror }}</div>
          <div
            v-show="
              !data.amterror &&
              data.repayamt &&
              !Number.isNaN(Number(data.repayamt)) &&
              Big(data.repayamt || 0) > 0
            "
          >
            <div class="cellLine">
              <div class="cellLeft">还本金</div>
              <div class="cellRight">
                {{
                  data.termloading
                    ? "计算中"
                    : "￥" + amountNumberFormat(data.trialinfo?.totalPrincipal)
                }}
              </div>
            </div>
            <div class="cellLine">
              <div class="cellLeft">利息</div>
              <div class="cellRight">
                {{
                  data.termloading
                    ? "计算中"
                    : "￥" + amountNumberFormat(data.trialinfo?.totalInterest)
                }}
              </div>
            </div>
            <div v-show="!Big(data.trialinfo?.totalDiscount || 0).eq(0)" class="cellLine">
              <div class="cellLeft">优惠券</div>
              <div class="cellRight">
                {{
                  data.termloading
                    ? "计算中"
                    : "-￥" + amountNumberFormat(data.trialinfo?.totalDiscount)
                }}
              </div>
            </div>
          </div>
          <myBank
            class="container1"
            title="还款银行卡"
            label="还款银行卡"
            :escapeinit="data.param.isdelay === 'true' && data.isall"
            agree="false"
            :apply-no="data.param?.outOrderNo"
            :is-reserve-phone-error="data.isReservePhoneError"
            :show-bank-transfer-card="showBankTransferCard"
            @bankinfo="bankinfo"
            @go-to-transfer-repay-page="goToTransferRepayPage"
          ></myBank>
        </div>
      </div>
    </div>
    <div class="footer">
      <hnr-button
        type="primary"
        class="content-bottom-btn"
        standard-width="true"
        :disabled="comfireButtonDisabled"
        @click="submit"
      >
        确认还款{{
          ((!(data.param.isdelay !== "true" && data.isall) &&
            data.repayamt &&
            !Number.isNaN(Number(data.repayamt)) &&
            Big(data.repayamt || 0) > 0) ||
            (data.param.isdelay !== "true" && data.isall)) &&
          !data.amterror
            ? realamt
            : ""
        }}
      </hnr-button>
    </div>
    <div v-show="data.btnloading">
      <hnr-dialog
        v-model:show="data.btnloading"
        title=""
        message=""
        button-direction="row"
        :before-close="disabledOverlayClose"
      >
        <template #footer>
          <div class="loading">
            <div class="loading-text">正在提交</div>
            <div class="loading-logo"><hnr-loading /></div>
          </div>
        </template>
      </hnr-dialog>
    </div>
    <hnr-dialog
      v-model:show="store.showQueryRepayStatusDialog"
      show-filter
      :show-confirm-button="false"
      :show-cancel-button="false"
    >
      <div class="query-dialog">正在获取还款信息<hnr-loading /></div>
    </hnr-dialog>
  </div>
</template>

<style scoped>
.bg {
  user-select: none;
  display: flex;
  flex-direction: column;
  position: fixed;
  background: var(--hnr-color-background-cardview);
  height: 100%;
  width: 100%;
}
.describe {
  margin: 0 var(--dp24);
  color: var(--hnr-text-color-primary);
  font: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
:deep(.hnr-field__input-area:before) {
  width: 0;
}
.errmsg {
  margin: 0 var(--hnr-elements-margin-vertical-M2);
  color: var(--hnr-color-error);
  font-size: calc(var(--hnr-body-3) / var(--hnr-large-font-rate));
  text-align: left;
}
.container {
  display: flex;
  flex-direction: column;
  /* margin-left: calc(24px - var(--hnr-list-card-padding-end)) !important; */
  /* margin-right: calc(24px - var(--hnr-list-card-padding-end)) !important; */
  width: calc(
    (100% - 48px) + var(--hnr-list-card-padding-end) + var(--hnr-list-card-padding-start)
  ) !important;
}
.container1 {
  margin: 0 var(--hnr-list-card-padding-start);
  display: flex;
  flex-direction: column;
}
.hnr-cell-box {
  width: 100% !important;
  padding: 0;
  margin: 0 !important;
}
.infotitle1 {
  /* padding-left: var(--hnr-list-card-padding-start); */
  padding-right: var(--hnr-list-card-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.infotitle2 {
  /* padding-left: var(--hnr-list-card-padding-start); */
  padding-right: var(--hnr-list-card-padding-end);
  font-size: var(--hnr-headline-8);
  color: var(--hnr-text-color-primary);
  font-weight: var(--hnr-font-weight-medium);
}
.infotitle3 {
  /* padding-left: var(--hnr-list-card-padding-start); */
  padding-right: var(--hnr-list-card-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-M);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.imgs {
  margin-top: var(--dp3) !important;
  vertical-align: middle;
  margin: 0 var(--hnr-elements-margin-horizontal-S);
  width: var(--dp14);
  height: var(--dp14);
  transform: translateY(2px);
}
.footer {
  display: flex;
  width: 100%;
  position: fixed;
  bottom: 0;
  z-index: 99;
  justify-content: center;
}
.rightvalue {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-end;
}
.tips {
  font-weight: var(--hnr-font-weight-medium);
  font-size: small;
}
.content {
  margin: 0 var(--hnr-elements-margin-vertical-M2);
  display: flex;
  align-items: center;
}
.hnr-button--text {
  /* display: contents !important; */
  padding: 0;
}
.rmb {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
  margin-right: var(--hnr-elements-margin-horizontal-M);
}
.line {
  height: var(--dp1);
  background-color: var(--hnr-list-divider);
  margin: 0 var(--hnr-elements-margin-vertical-M2);
}
:deep(.rightvalue) {
  flex-direction: column;
}
.accountNum {
  background-color: var(--hnr-color-list-card-background);
  border-radius: var(--hnr-card-corner-radius);
  margin: var(--hnr-elements-margin-vertical-M2);
  padding-top: var(--hnr-elements-margin-vertical-M2);
}
.title1 {
  margin: 0 var(--hnr-elements-margin-vertical-M2);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.content-bottom-btn {
  max-width: none !important;
  margin: var(--hnr-elements-margin-vertical-M2) 0 var(--hnr-default-padding-bottom-fixed) 0;
}
:deep(.hnr-field__control) {
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-regular);
}
:deep(.hnr-field__control::-webkit-input-placeholder) {
  /* placeholder颜色  */
  color: var(--hnr-text-color-tertiary);
  /* placeholder字体大小  */
  font-size: var(--hnr-font-weight-regular);
  /* placeholder位置  */
  /* text-align: right; */
}
.fields {
  background-color: var(--hnr-color-list-card-background);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-6);
  font-weight: var(--hnr-font-weight-medium);
  caret-color: var(--hnr-color-accent);
  border: none;
  outline: none;
  width: 100%;
}
:deep(.hnr-cell__value) {
  font-weight: bold;
  margin: 0;
  /* min-width: 20% !important; */
}
:deep(.hnr-dialog__title) {
  font-weight: bold;
}
:deep(.hnr-cell__prefix--title) {
  color: #256fff;
}
.cellLine {
  width: 100%;
  position: relative;
  display: flex;
  box-sizing: border-box;
  overflow: hidden;
  justify-content: space-between;
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-start);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.cellLeft {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.cellRight {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.loanall-button {
  display: flex;
  flex-direction: row-reverse;
  width: 8rem;
}
.fields {
  background-color: var(--hnr-color-list-card-background);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-6);
  font-weight: var(--hnr-font-weight-medium);
  caret-color: var(--hnr-color-accent);
  border: none;
  outline: none;
  width: 100%;
}
:deep(.hnr-field--withMargin) {
  width: 100%;
  margin: 0;
}
:deep(.hnr-cell-group) {
  /* margin-left: calc(24px - var(--hnr-list-card-padding-end)) !important; */
  margin-right: calc(24px - var(--hnr-list-card-padding-end)) !important;
}
.loading {
  width: 100%;
  height: calc(80 * var(--dp));
  display: flex;
  justify-content: space-around;
  align-items: center;
  box-sizing: border-box;
  padding-top: var(--dp6);
  padding-bottom: var(--dp14);
}
.loading-text {
  display: inline-block;
  width: 70%;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}

/* 深色模式下的图标反色处理 */
@media (prefers-color-scheme: dark) {
  .imgs {
    filter: invert(1);
  }
}
</style>
<style>
.violateFee-bubble-tip {
  top: 353px !important;
}

.violateInterest-bubble-tip {
  top: 283px !important;
}
</style>
