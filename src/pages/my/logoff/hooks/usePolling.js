import { ref, onMounted, onUnmounted } from 'vue';


export function usePolling(fetchDataFn, stopConditionFn, interval = 1000, initialData = null) {
  const data = ref(initialData);
  const isPolling = ref(false);
  const error = ref(null);
  let timer = null;

  // 内部函数：执行一次数据获取并检查停止条件
  const poll = async () => {
    error.value = null;
    try {
      const result = await fetchDataFn();
      data.value = result;

      if (stopConditionFn(result)) {
        stop();
      }
    } catch (err) {
      console.error('Polling error:', err);
      error.value = err;
      stop();
    }
  };

  // 启动轮询
  const start = async () => {
    if (timer) return;
    isPolling.value = true;

    await poll();

    timer = setInterval(poll, interval);
  };

  // 停止轮询
  const stop = () => {
    if (timer) {
      clearInterval(timer); // 清除定时器
      timer = null;
      isPolling.value = false;
    }
  };

  onMounted(() => {
    if (!stopConditionFn(initialData)) {
      start();
    }
  });

  // 组件卸载时自动停止轮询，防止内存泄漏
  onUnmounted(stop);

  // 返回暴露给组件的状态和方法
  return {
    data,
    isPolling,
    error,
    start,
    stop,
  };
}
