<script setup>
import {
  onServerPrefetch,
  ref,
  watchEffect,
  computed,
  watch,
  onMounted,
  nextTick,
  onUnmounted,
} from "vue";
import { showToast, showDialog } from "@hihonor/hnr/dist/hnr.es.min";
import lodash from "lodash";
import useStore from "./store";
import { initStore, request } from "../../../helpers/utils";
import {
  back,
  goto,
  enableBackPress,
  regNativeEvent,
  callPhone,
  report,
} from "../../../helpers/native-bridge";
import { del, getStore, setWithExactExpireUnit } from "../../../helpers/storage";
import IcsvgPublicBackFilled from "../../../components/svg/icsvgPublicBackFilled.vue";
import { DEBOUNCE_OPTIONS, DEBOUNCE_WAIT_MILLISECONDS } from "../../../helpers/constants";

const { store, data } = initStore(useStore);
onServerPrefetch(store.initial);
// 顶部标题文本
const title = ref("注销借钱服务");
// 标题监听
watchEffect(() => {
  title.value = "注销借钱服务";
});
// 信息权益变化页
const isInfoChangePage = ref(false);
const info = ref([
  {
    title: "删除当前应用服务痕迹",
    tips: "删除用户在荣耀钱包的相关借贷服务订单信息，包括交易、还款信息等。若需注销金融合作伙伴的注册用户信息，请联系合作伙伴客服进行处理。",
  },
  {
    title: "删除相关权益",
    tips: "将清空相关权益（优惠劵等）及消费额度，后续无法继续享用相关权益和消费额度。",
  },
  {
    title: "不再提供咨询和售后服务",
    tips: "后续将不再提供关于该账号的相关咨询和售后服务。",
  },
]);

function emptyStorage() {
  del(`U_MY_LOGOFF_TIPS_${data.value.param.userId}`);
  del(`U_MY_LOGOFF_REASON_${data.value.param.userId}`);
  del(`U_MY_LOGOFF_VERIFI_${data.value.param.userId}`);
  del(`U_MY_LOGOFF_SUCCESS_${data.value.param.userId}`);
  const storage = getStore(`U_LOAN_PROCESS_${data.value.param.userId}`) || null;
  if (storage && storage.openId) del(`U_LOAN_INFO_STORE_${storage.openId}`);
}
function getStorageIsTips() {
  return getStore(`U_MY_LOGOFF_TIPS_${data.value.param.userId}`) || "";
}
function getStorageIsReason() {
  return getStore(`U_MY_LOGOFF_REASON_${data.value.param.userId}`) || "";
}
function getStorageIsVerifi() {
  return getStore(`U_MY_LOGOFF_VERIFI_${data.value.param.userId}`) || "";
}
function getStorageIsSuccess() {
  return getStore(`U_MY_LOGOFF_SUCCESS_${data.value.param.userId}`) || "";
}

// 提示页
const isTipsPage = ref(false);
const conditionLlistText = ref([
  "账号内所有订单状态已完成，不存在流程中的授信单和交易单;",
  "所有的借款订单均已结清。",
]);
const messageFail = ref("当前账户不满足注销条件，请稍后重试。");
// 注销失败弹窗
const logoffFailDialog = ref(false);
// 提示页下一步按钮
// 确认注销按钮
function logOff() {
  isInfoChangePage.value = false;
  isTipsPage.value = true;
  window.location.hash = "isTipsPage";
  emptyStorage();
  setWithExactExpireUnit(`U_MY_LOGOFF_TIPS_${data.value.param.userId}`, true, 7, "D");
}
// 填写注销原因页

const formatter = (value) =>
  value.replace(/[\uD83C[\uDF00-\uDFFF]|\uD83D[\uDC00-\uDE4F]|[!@#$%^&*(),.?":{}|<>]/g, "");
const isReasonPage = ref(false);
async function onClickNext() {
  const res = await request(
    "/loan/api/user/logoff/check",
    {},
    {
      mock: false,
    },
  );
  if (res.code === 0) {
    if (res.data?.logoffAllowed) {
      isReasonPage.value = true;
      isTipsPage.value = false;
      window.location.hash = "isReasonPage";
      emptyStorage();
      setWithExactExpireUnit(`U_MY_LOGOFF_REASON_${data.value.param.userId}`, true, 7, "D");
    } else {
      messageFail.value = "当前账号不满足注销条件，请稍后重试。";
      logoffFailDialog.value = true;
    }
  } else {
    showToast({ message: res.message });
  }
}

// 复选框
const checked = ref([]);
const checkboxRefs = ref([]);
// 复选框list
const list = computed(() => {
  return [1, 3].includes(data.value?.realNameInfo?.status)
    ? ["暂时没有借款需求", "额度不够用", "利率没有吸引力", "有额度但无法借款", "其他"]
    : [
        "暂时没有借款需求",
        "额度不够用",
        "利率没有吸引力",
        "申请额度失败",
        "有额度但无法借款",
        "其他",
      ];
});

// 是否展示文本域输入框
const isShowReasonText = ref(false);
// 手动填写原因
const reasonText = ref("");
const contentReason = ref();
const disabledStyle = ref({ opacity: 0.3 });
// 复选框绑定
const toggle = (index) => {
  if (checked.value.length === 0) {
    checkboxRefs.value[index].toggle();
  } else if (checked.value.includes("申请额度失败")) {
    if (checkboxRefs.value[index].name === "申请额度失败") {
      checkboxRefs.value[index].toggle();
    }
  } else if (checkboxRefs.value[index].name !== "申请额度失败") {
    checkboxRefs.value[index].toggle();
  }
  // 滚动到底部
  if (index === 5) {
    nextTick(() => {
      const contentReasonElem = contentReason.value;
      contentReasonElem.scrollTo({ top: contentReasonElem.scrollHeight, behavior: "smooth" });
    });
  }
};

// 滚动到底部
function clickInput() {
  nextTick(() => {
    const contentReasonElem = contentReason.value;
    contentReasonElem.scrollTo({ top: contentReasonElem.scrollHeight, behavior: "smooth" });
  });
}
// 下一步按钮是否高亮
const disabledReason = ref(true);
watchEffect(() => {
  if (checked.value.includes("其他")) {
    isShowReasonText.value = true;
  } else {
    isShowReasonText.value = false;
  }
  if (checked.value.length) {
    disabledReason.value = false;
  } else {
    disabledReason.value = true;
  }
});
const isVerificationCode = ref(false);
// 填写原因页按钮
const onClickNextReason = lodash.debounce(
  async () => {
    isReasonPage.value = false;
    isVerificationCode.value = true;
    window.location.hash = "isVerificationCode";
    emptyStorage();
    setWithExactExpireUnit(`U_MY_LOGOFF_VERIFI_${data.value.param.userId}`, true, 7, "D");
    report("wallet_page_result", {
      result_name: "logoff_reason",
      reason_type: checked.value,
      other_reason: reasonText.value,
    });
  },
  DEBOUNCE_WAIT_MILLISECONDS,
  DEBOUNCE_OPTIONS,
);
// 手机号做脱敏处理
function phoneChange(phone) {
  const reg = /^(1[3-9][0-9])\d{4}(\d{4}$)/; // 定义手机号正则表达式
  const newPhone = phone.replace(reg, "$1****$2");
  return newPhone; // 185****6696
}
// 短信验证码页
const codeValue = ref("");
const codeDisabled = ref(true);
const mobileNo = computed(() => {
  if (data.value?.realNameInfo?.mobileNo) {
    return phoneChange(data.value?.realNameInfo?.mobileNo);
  }
  return "";
});
// 倒计时秒数
const countdown = ref(0);
const codeErrorMessage = ref("");
// 确认按钮Disabled
watchEffect(() => {
  if (!codeValue.value) {
    codeDisabled.value = true;
    codeErrorMessage.value = "";
  } else if (!/^\d+$/.test(codeValue.value)) {
    codeDisabled.value = true;
    codeErrorMessage.value = "验证码格式不对";
  } else {
    codeDisabled.value = false;
  }
});
// 验证码倒计时
const startCountdown = () => {
  if (countdown.value > 0) {
    return;
  }
  store.sendVerifyCode();
  countdown.value = 59; // 倒计时60秒
  const intervalId = setInterval(() => {
    if (countdown.value > 0) {
      countdown.value -= 1;
    } else {
      clearInterval(intervalId);
    }
  }, 1000);
};
watch(
  isVerificationCode,
  (newVal) => {
    if (newVal) {
      startCountdown();
    }
  },
  {
    once: true,
  },
);

const codeChange = () => {
  codeErrorMessage.value = "";
};

const islogoffSuccess = ref(false);
// 验证码后端校验
const requestNextCode = async () => {
  if (store.supplierName === '随身贷') {
    store.isProcessingLogoff = true;
  }
  store.requestLoading = true;
  const params = {
    code: codeValue.value,
  };
  const res = await request("/loan/api/user/v2/logoff", params, {
    mock: false,
  });
  if (res.code === 0) {
    if (res.data.logoffResult) {
      if (store.supplierName === '随身贷') {
        store.userLogoffStatus = 4;


      }else {
        del(`U_LOAN_PROCESS_${data.value.param.userId}`);
        del(`U_CREDIT_PROCESS_${data.value.param.userId}`);
        del(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`);
        window.location.hash = "islogoffSuccess";
        setWithExactExpireUnit(`U_MY_LOGOFF_SUCCESS_${data.value.param.userId}`, true, 7, "D");
        isReasonPage.value = false;
        isTipsPage.value = false;
        isInfoChangePage.value = false;
        isVerificationCode.value = false;
        islogoffSuccess.value = true; // 去到注销成功页
        enableBackPress(false);
      }
    } else {
      showDialog({ message: res.data.logoffErrDesc });
    }
  } else {
    codeErrorMessage.value = res.message || "请输入正确的验证码";
  }
  store.requestLoading = false;
};
const onClickNextCode = lodash.debounce(
  () => {
    requestNextCode();
  },
  1000,
  DEBOUNCE_OPTIONS,
);

// 注销成功页

// 注销成功点击完成回到首页
const onClickFinish = () => {
  emptyStorage();
  goto("/wallet-loan-web/pages/index");
};

// 回退
function onClickLeft() {
  if (isInfoChangePage.value) {
    back();
  } else if (islogoffSuccess.value) {
    emptyStorage();
    goto("/wallet-loan-web/pages/index");
  } else if (isTipsPage.value) {
    emptyStorage();
    isTipsPage.value = false;
    isInfoChangePage.value = true;
    window.history.back();
  } else if (isReasonPage.value) {
    isReasonPage.value = false;
    isTipsPage.value = true;
    emptyStorage();
    setWithExactExpireUnit(`U_MY_LOGOFF_TIPS_${data.value.param.userId}`, true, 7, "D");
    window.history.back();
  } else {
    isVerificationCode.value = false;
    isReasonPage.value = true;
    emptyStorage();
    setWithExactExpireUnit(`U_MY_LOGOFF_REASON_${data.value.param.userId}`, true, 7, "D");
    window.history.back();
  }
}

const callSupplierContact = () => {
  callPhone(data.value.supplierContact);
};

const gotoApplicationAmount = () => {
  del(`U_CREDIT_PROCESS_V2_${data.value.param.userId}`);
  goto("/wallet-loan-web/pages/credit/realname?fromPage=logOffSuccess");
};
regNativeEvent("onBack", () => {
  back();
});

onMounted(async () => {
  setTimeout(() => {
    report("wallet_page_view", {
      page_name: "my_logoff_page",
    });
  });
  window.addEventListener("hashchange", () => {
    if (islogoffSuccess.value && window.location.hash !== "#islogoffSuccess") {
      emptyStorage();
      goto("/wallet-loan-web/pages/index");
      return;
    }
    if (window.location.hash === "#isInfoChangePage") {
      isTipsPage.value = false;
      isInfoChangePage.value = true;
      emptyStorage();
    } else if (window.location.hash === "#isTipsPage") {
      isTipsPage.value = true;
      emptyStorage();
      setWithExactExpireUnit(`U_MY_LOGOFF_TIPS_${data.value.param.userId}`, true, 7, "D");
      isInfoChangePage.value = false;
      isReasonPage.value = false;
    } else if (window.location.hash === "#isReasonPage") {
      isReasonPage.value = true;
      emptyStorage();
      setWithExactExpireUnit(`U_MY_LOGOFF_REASON_${data.value.param.userId}`, true, 7, "D");
      isTipsPage.value = false;
      isInfoChangePage.value = false;
      isVerificationCode.value = false;
    } else if (window.location.hash === "#isVerificationCode") {
      isVerificationCode.value = true;
      emptyStorage();
      setWithExactExpireUnit(`U_MY_LOGOFF_VERIFI_${data.value.param.userId}`, true, 7, "D");
      isReasonPage.value = false;
      isTipsPage.value = false;
      isInfoChangePage.value = false;
    } else {
      emptyStorage();
      isVerificationCode.value = false;
      isReasonPage.value = false;
      isTipsPage.value = false;
      if (!window.location.hash) {
        isInfoChangePage.value = true;
      }
    }
  });

  const storageIsTips = await getStorageIsTips();
  const storageIsReason = await getStorageIsReason();
  const storageIsVerifi = await getStorageIsVerifi();
  const storageIsSuccess = await getStorageIsSuccess();
  setTimeout(() => {
    if (!window.location.hash && storageIsTips) {
      window.location.hash = "isTipsPage";
    }
    if (!window.location.hash && storageIsReason) {
      window.location.hash = "isTipsPage";
      window.location.hash = "isReasonPage";
    }
    if (!window.location.hash && storageIsVerifi) {
      window.location.hash = "isTipsPage";
      window.location.hash = "isReasonPage";
      window.location.hash = "isVerificationCode";
    }
    if (!window.location.hash && storageIsSuccess) {
      window.location.hash = "isTipsPage";
      window.location.hash = "isReasonPage";
      window.location.hash = "isVerificationCode";
      window.location.hash = "islogoffSuccess";
    }
  }, 0);
  if (storageIsTips) {
    isInfoChangePage.value = false;
    isTipsPage.value = true;
  } else if (storageIsReason) {
    isInfoChangePage.value = false;
    isReasonPage.value = true;
  } else if (storageIsVerifi) {
    isInfoChangePage.value = false;
    isVerificationCode.value = true;
  } else if (storageIsSuccess) {
    isInfoChangePage.value = false;
    islogoffSuccess.value = true; // 去到注销成功页
  } else {
    isInfoChangePage.value = true;
  }
});

onUnmounted(() => {
  emptyStorage();
});
</script>

<template>
  <div class="main">
    <div class="area">
      <!-- 总标题 -->
      <div class="header">
        <div class="header-title">
          <hnr-nav-bar :title="title" class="nav-padding-top" transparent="true">
            <template #left>
              <icsvg-public-back-filled @click="onClickLeft"></icsvg-public-back-filled>
            </template>
          </hnr-nav-bar>
        </div>
      </div>
      <!-- 信息权益变化页 -->
      <template v-if="isInfoChangePage">
        <div class="content content-info">
          <div class="Info-title">注销后，以下信息会发生变化</div>
          <ul>
            <li v-for="item in info" :key="item.title" class="list-item">
              <div class="item-top">
                <div class="dot-box">
                  <span class="dot"></span>
                </div>
                {{ item.title }}
              </div>
              <div class="item-bottom">
                {{ item.tips }}
              </div>
            </li>
          </ul>
        </div>
        <div class="bottom">
          <hnr-button type="default" class="bottom-top" @click="logOff">确认注销</hnr-button>
          <hnr-button type="primary" @click="onClickLeft">暂不注销</hnr-button>
        </div>
      </template>
      <!-- 提示页 -->
      <div v-if="isTipsPage" class="content content-tips">
        <div class="logo">
          <!-- <img src="../../../../public/my/logoff.svg" alt=""> -->
          <span class="icon-logoff"></span>
        </div>
        <div class="logoff-title">注销须知</div>
        <div class="logoff-tips">
          为确保账户和资金安全，避免产生资金和售后纠纷，注销账号需满足以下条件:
        </div>
        <div v-for="(item, index) in conditionLlistText" :key="index" class="logoff-condition">
          {{ index + 1 }}.{{ item }}
        </div>
        <div class="bottom">
          <hnr-button type="primary" class="main-button" @click="onClickNext">下一步</hnr-button>
        </div>
        <hnr-dialog
          v-model:show="logoffFailDialog"
          title="注销失败"
          :message="messageFail"
          button-direction="row"
          :narrow-padding="true"
          show-confirm-button="true"
          confirm-button-type="text"
          confirm-button-text="知道了"
          @confirm="() => (logoffFailDialog = !logoffFailDialog)"
        >
        </hnr-dialog>
      </div>
      <!-- 填写注销原因页 -->
      <div v-if="isReasonPage" ref="contentReason" class="content content-reason">
        <div class="reason-title">请您选择注销原因，让我们做的更好</div>
        <hnr-checkbox-group v-model="checked">
          <hnr-cell-group inset>
            <template v-for="(item, index) in list" :key="item">
              <hnr-cell
                v-if="item !== '申请额度失败'"
                border
                clickable
                :title-style="checked.includes('申请额度失败') ? disabledStyle : {}"
                :title="item"
                :class="'hnr-cell-' + index"
                @click="toggle(index)"
              >
                <template #value>
                  <hnr-checkbox
                    :ref="(el) => (checkboxRefs[index] = el)"
                    :name="item"
                    :disabled="checked.includes('申请额度失败')"
                    @click.stop
                  />
                </template>
              </hnr-cell>
              <hnr-cell
                v-else
                border
                clickable
                :title-style="
                  !(checked.length === 0 || (checked.length === 1 && checked[0] === '申请额度失败'))
                    ? disabledStyle
                    : {}
                "
                :title="item"
                :class="'hnr-cell-' + index"
                @click="toggle(index)"
              >
                <template #value>
                  <hnr-checkbox
                    :ref="(el) => (checkboxRefs[index] = el)"
                    :name="item"
                    :disabled="
                      !(
                        checked.length === 0 ||
                        (checked.length === 1 && checked[0] === '申请额度失败')
                      )
                    "
                    @click.stop
                  />
                </template>
              </hnr-cell>
            </template>

            <div v-if="isShowReasonText" class="reason-text">
              <hnr-field
                v-model="reasonText"
                :formatter="formatter"
                border
                type="textarea"
                rows="3"
                show-word-limit
                :maxlength="50"
                placeholder="请输入注销原因(50字内)"
                @click-input="clickInput"
                @focus="clickInput"
              />
            </div>
          </hnr-cell-group>
        </hnr-checkbox-group>
      </div>
      <!-- 短信验证码页 -->
      <template v-if="isVerificationCode">
        <div class="content content-verificationCode">
          <div class="verificationCode-title">输入短信验证码</div>
          <div class="verificationCode-tips">已发送至手机号{{ mobileNo }}</div>
          <hnr-card type="empty">
            <hnr-field
              v-model="codeValue"
              placeholder="短信验证码"
              :error-message="codeErrorMessage"
              class="verification-code"
              @update:model-value="codeChange"
            >
              <template #button>
                <hnr-button
                  type="text"
                  size="mini"
                  :disabled="countdown > 0"
                  @click="startCountdown"
                  >{{ countdown === 0 ? "获取验证码" : `${countdown}s` }}</hnr-button
                >
              </template>
            </hnr-field>
          </hnr-card>
        </div>
        <div class="bottom">
          <hnr-button
            type="primary"
            class="main-button bottom-verificationCode"
            :disabled="codeDisabled"
            :loading="store.requestLoading"
            @click="onClickNextCode"
            >确认注销</hnr-button
          >
        </div>

        <hnr-dialog
            v-model:show="store.isProcessingLogoff"
            show-filter
            :show-confirm-button="false"
            :show-cancel-button="false"
        >
          <div class="query-dialog">借钱服务注销中<hnr-loading /></div>
        </hnr-dialog>
      </template>
      <!-- 注销成功页 -->
      <template v-if="islogoffSuccess">
        <div class="content content-logoff-success">
          <div class="logo">
            <img src="../../../../public/my/success.svg" alt="" />
          </div>
          <div class="success-title">注销成功</div>
          <template v-if="data.supplierName !== 'undefined'">

            <span v-if="data.supplierName === '随身贷'">您已注销荣耀借钱服务</span>

            <span v-else class="content-line1"
              >您已注销荣耀借钱服务。如需注销{{ data.supplierName }}信贷额度，请咨询{{
                data.supplierName
              }}客服<span class="call-supplier-contact" @click="callSupplierContact">{{
                data.supplierContact
              }}</span></span
            >



            <span class="content-line2"
              >如仍有借钱需求，可申请其他荣耀合作机构的信贷额度。<span
                class="call-supplier-contact"
                @click="gotoApplicationAmount"
                >申请额度</span
              ></span
            >
          </template>
          <template v-else>
            <span class="content-line1"
              >您已注销荣耀借钱服务。如仍有借钱需求，可<span
                class="call-supplier-contact"
                @click="gotoApplicationAmount"
                >重新申请</span
              ></span
            >
          </template>
        </div>
        <div class="bottom">
          <hnr-button
            type="primary"
            class="main-button bottom-verificationCode"
            @click="onClickFinish"
            >完成</hnr-button
          >
        </div>
      </template>
    </div>
    <div v-if="isReasonPage" class="bottom content-reason-bottom">
      <hnr-button
        type="primary"
        class="main-button"
        :disabled="disabledReason"
        :loading="store.requestLoading"
        @click="onClickNextReason"
        >下一步</hnr-button
      >
    </div>
  </div>
</template>

<style>
:root {
  --dp: 1px;
}
</style>
<style scoped>
.main {
  position: relative;
  display: flex;
  flex-direction: column;
  user-select: none;
}
.area {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  width: 100vw;
  height: calc(100vh - 36 * var(--dp));
  padding-bottom: var(--hnr-default-padding-bottom-fixed);
  top: 0.5rem;
}
.header {
  box-sizing: border-box;
}
.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  box-sizing: border-box;
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
}
.content-line1 {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-top: var(--hnr-elements-margin-vertical-M2);
}
.content-line2 {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.call-supplier-contact {
  font-size: 14px;
  color: #256fff;
  text-align: center;
  line-height: 19px;
  font-weight: 500;
}
ul,
li {
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
}
.bottom {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  justify-content: space-around;
  position: fixed;
  bottom: var(--hnr-default-padding-bottom-fixed);
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
}
.main-button {
  height: calc(36 * var(--dp));
}
.content-info {
  align-items: start;
}
.content-tips {
  align-items: center;
}
.content-reason {
  box-sizing: border-box;
  padding-left: 0;
  padding-right: 0;
}
.content-verificationCode {
  padding-left: 0;
  padding-right: 0;
}
.content-reason-bottom {
  position: relative;
}
.content-logoff-success {
  align-items: center;
}
.Info-title {
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-bottom: var(--hnr-elements-margin-vertical-L2);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
}
.list-item {
  list-style-type: square;
  margin-bottom: var(--hnr-elements-margin-vertical-L2) !important;
}
.list-item::marker {
  color: var(--hnr-text-color-primary);
}
.item-top {
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
  display: flex;
  align-items: center;
}
.dot-box {
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.dot {
  width: 3px;
  height: 3px;
  background-color: #151517;
}
.item-bottom {
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
.content-top {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.logo {
  width: calc(64 * var(--dp));
  height: calc(64 * var(--dp));
  margin-top: var(--hnr-elements-margin-vertical-XXL);
  margin-bottom: var(--hnr-elements-margin-vertical-L);
}
.logoff-title {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-L2);
}
.logoff-tips {
  width: 100%;
  text-align: start;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
.logoff-condition {
  width: 100%;
  text-align: start;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-medium);
  margin-bottom: var(--hnr-elements-margin-vertical-M2);
}
::v-deep(.hnr-dialog__content--narrow-padding) {
  padding: var(--hnr-dialog-header-padding);
}
.reason-title {
  width: 100%;
  box-sizing: border-box;
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-bottom: var(--hnr-elements-margin-vertical-M);
  padding-left: calc(
    (calc(24 * var(--dp)) - var(--hnr-list-card-padding-end)) + var(--hnr-list-card-padding-start)
  );
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-subtitle-2);
  font-weight: var(--hnr-font-weight-medium);
}
.reason-text {
  width: 100%;
  box-sizing: border-box;
  margin-top: var(--hnr-elements-margin-vertical-L);
  margin-bottom: var(--hnr-elements-margin-vertical-L2);
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-1);
  font-weight: var(--hnr-font-weight-regular);
}
::v-deep(.hnr-field--withMargin) {
  width: calc(100% - var(--hnr-list-card-padding-start) - var(--hnr-list-card-padding-end));
  margin: var(--hnr-list-card-padding-start);
}
::v-deep(#hnr-field-1-input) {
  color: var(--hnr-text-color-secondary) !important;
  font-size: var(--hnr-body-1) !important;
  font-weight: var(--hnr-font-weight-regular) !important;
}
::v-deep(.hnr-field__word-limit) {
  color: var(--hnr-text-color-tertiary);
  font-size: var(--hnr-body-2);
  font-weight: var(--hnr-font-weight-regular);
}
::v-deep(.hnr-field__control) {
  color: var(--hnr-color-control-normal);
  border-radius: 0 !important;
}
.verification-code /deep/.hnr-field__input-area:before {
  height: 0;
}
.verificationCode-title {
  width: 100%;
  box-sizing: border-box;
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
  margin-top: var(--hnr-elements-margin-vertical-L2);
  margin-bottom: calc(4 * var(--dp));
  text-align: center;
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-8);
  font-weight: var(--hnr-font-weight-medium);
}
.verificationCode-tips {
  width: 100%;
  box-sizing: border-box;
  padding-left: var(--hnr-max-padding-start);
  padding-right: var(--hnr-max-padding-end);
  margin-bottom: calc(36 * var(--dp));
  text-align: center;
  color: var(--hnr-text-color-secondary);
  font-size: var(--hnr-body-3);
  font-weight: var(--hnr-font-weight-regular);
}
.verification-code {
  width: 100%;
  box-sizing: border-box;
  padding-left: var(--hnr-list-card-padding-start);
  padding-right: var(--hnr-list-card-padding-end);
  margin: 0;
}
::v-deep(.hnr-card) {
  width: 100%;
  box-sizing: border-box;
}
.bottom-verificationCode {
  width: 100%;
  max-width: none !important;
}
.success-title {
  color: var(--hnr-text-color-primary);
  font-size: var(--hnr-headline-7);
  font-weight: var(--hnr-font-weight-medium);
}
.success-title .main-button {
  width: 100%;
}
::v-deep(.hnr-dialog__content--narrow-padding) {
  padding: var(--hnr-dialog-header-padding);
  padding-top: 0;
  padding-bottom: 0;
}
.icon-logoff:before {
  content: "\e94b";
  font-family: "icomoon";
  font-size: var(--dp64);
  color: var(--hnr-magic-gray-4);
}
.content-tips /deep/.hnr-dialog {
  width: 100% !important;
}
.content-tips /deep/.hnr-popup--center {
  top: calc(100vh - 80 * var(--dp));
}
.content-reason /deep/ .hnr-cell-5 .hnr-cell:before {
  content: none;
}
.bottom-top {
  background-color: var(--hnr-color-toast-background);
}
::v-deep(.hnr-field__error-message) {
  position: absolute;
  bottom: -27px;
  left: 12px;
}
@media (prefers-color-scheme: dark) {
  .dot {
    background-color: #eaeae8;
  }
}
</style>
