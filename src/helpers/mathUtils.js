import Big from "big.js";

/**
 * 判断一个值是否可以被视为一个有限的数字。
 * @param {*} value - 需要检查的值。
 * @returns {boolean} 如果值是或可以转换为一个有限数字，则返回 true；否则返回 false。
 */
export function isNumber(value) {
  if (typeof value === "number") {
    return Number.isFinite(value);
  }

  if (typeof value !== "string") {
    return false;
  }

  const str = value.trim();

  if (str === "") {
    return false;
  }

  const num = Number(str);

  return !Number.isNaN(num) && Number.isFinite(num);
}

// 金额格式化，例如 1000000 -> 10,000.00
export function formatCurrencyAmount(num) {
  return (num / 100).toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
}

export const convertFenToYuan = (fenAmount) => {
  const amount = parseFloat(fenAmount) || 0;
  return Big(amount).div(100).toFixed(2);
};


export const convertYuanToFen = (yuanAmount) => {
  const amount = parseFloat(yuanAmount) || 0;
  return Big(amount).times(100).toFixed(0);
};
