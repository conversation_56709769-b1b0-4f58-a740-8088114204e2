// src/pages/repayment/apply/index.spec.ts
import { mount } from "@vue/test-utils";
import { beforeEach, describe, it, expect, vi } from "vitest";
import Index from "./index.vue";

describe("Repayment Apply Page", () => {
  let wrapper;
  let mockStore;

  beforeEach(() => {
    mockStore = {
      initial: vi.fn(),
      parseSupportRepayType: vi.fn().mockReturnValue(["type1"]),
      getSupportRepayEnable: vi.fn().mockReturnValue(true),
      btn1: "Button 1",
      btn2: "Button 2",
      supportRepayType: [
        { type: 1, disabled: false },
        { type: 2, disabled: false },
        { type: 3, disabled: false },
        { type: 4, disabled: false },
        { type: 5, disabled: false },
      ],
    };

    wrapper = mount(Index, {
      global: {
        provide: {
          store: mockStore,
          data: {
            value: {
              isdelay: false,
              supportRepayType: mockStore.supportRepayType,
              isTokenExpired: false,
              param: { userId: "testUserId", outOrderNo: "testOutOrderNo" },
              resdata: {},
              delaydays: 0,
              delayinfos: {
                Principal: 100,
                Interest: 20,
                serviceFee: 5,
                Terms: [1],
                OverdueFee: 10,
                ReductionAmount: 0,
              },
              issuedetail: {
                termNo: 1,
                totalTerm: 12,
                termAmount: 120,
                termPrincipal: 100,
                termInterest: 20,
                termServiceFee: 5,
                shouldRepayDate: "2023-12-31",
                paidTermAmount: 0,
                paidTermPrincipal: 0,
                paidTermInterest: 0,
                paidTermServiceFee: 0,
                paidPenalty: 0,
                termReductionAmount: 0,
                reductionAmountDesc: "",
              },
              active1: {
                repayPlanTerms: [
                  {
                    label: "Term 1",
                    title: "First Term",
                    termNo: 1,
                    termAmount: 120,
                    termPrincipal: 100,
                    termInterest: 20,
                    termServiceFee: 5,
                    termPenalty: 0,
                    termReductionAmount: 0,
                    shouldRepayDate: "2023-12-31",
                  },
                ],
              },
              clear: false,
              istoday: false,
              random: "randomValue",
              deductionEarlyMorning: 0,
              showDueTip: 0,
              repayViolateFeeExplanation: "This is an explanation.",
              interestFeeExplanation: "This is an interestFeeExplanation.",
            },
          },
        },
      },
    });
  });

  it("renders the component correctly", () => {
    expect(wrapper.exists()).toBe(true);
  });

  it("calls store.initial on server prefetch", async () => {
    await wrapper.vm.onServerPrefetch();
    expect(mockStore.initial).toHaveBeenCalled();
  });

  it("sets startWeb to visible after timeout", async () => {
    expect(wrapper.vm.startWeb).toBe("hidden");
    await new Promise((resolve) => setTimeout(resolve, 100));
    expect(wrapper.vm.startWeb).toBe("visible");
  });

  it("handles onClickLeft correctly", () => {
    const backMock = vi.fn();
    vi.mock("../../../../src/helpers/native-bridge", () => ({
      back: backMock,
    }));

    wrapper.vm.onClickLeft();
    expect(backMock).toHaveBeenCalled();
  });

  it("navigates to correct URL when gotodetail is called without delay", () => {
    const gotoMock = vi.fn();
    vi.mock("../../../../src/helpers/native-bridge", () => ({
      goto: gotoMock,
    }));

    wrapper.vm.gotodetail();
    expect(gotoMock).toHaveBeenCalledWith(
      "/wallet-loan-web/pages/repayment/trial?isdelay=false&allTerm=12&outOrderNo=testOutOrderNo&repayTerm=1&repayAmount=120&repayPart=false&random=randomValue&RepayDate=2023-12-31&serviceFee=5&repayType=Button%202",
    );
  });

  it("navigates to correct URL when gotosettle is called without delay", () => {
    const gotoMock = vi.fn();
    vi.mock("../../../../src/helpers/native-bridge", () => ({
      goto: gotoMock,
    }));

    wrapper.vm.gotosettle();
    expect(gotoMock).toHaveBeenCalledWith(
      "/wallet-loan-web/pages/repayment/settle?isdelay=false&allTerm=12&outOrderNo=testOutOrderNo&repayTerm=1&repayAmount=120&repayPart=false&random=false&period=1&repayType=Button%201",
    );
  });

  it("disables button 1 when appropriate conditions are met", () => {
    wrapper.setProps({
      data: {
        value: {
          ...wrapper.props().data.value,
          supportRepayType: [
            { type: 1, disabled: true },
            { type: 2, disabled: true },
          ],
        },
      },
    });

    expect(wrapper.vm.btn1Disabled.value).toBe(true);
  });

  it("enables button 1 when appropriate conditions are met", () => {
    wrapper.setProps({
      data: {
        value: {
          ...wrapper.props().data.value,
          supportRepayType: [
            { type: 1, disabled: false },
            { type: 2, disabled: false },
          ],
        },
      },
    });

    expect(wrapper.vm.btn1Disabled.value).toBe(false);
  });

  it("displays the correct repayment details based on delay status", () => {
    expect(wrapper.find(".infotitle1").text()).toContain("1/12期(本期)应还");

    wrapper.setProps({
      data: {
        value: {
          ...wrapper.props().data.value,
          isdelay: true,
        },
      },
    });

    expect(wrapper.find(".infotitle1").text()).toContain("已逾期0天，逾期欠款");
  });

  it("reports events correctly", () => {
    const reportMock = vi.fn();

    wrapper.vm.gotodetail();
    expect(reportMock).toHaveBeenCalledWith("wallet_page_click", {
      click_name: "repayment_apply_repay_click",
      repayType: "Button 2",
    });

    wrapper.vm.gotosettle();
    expect(reportMock).toHaveBeenCalledWith("wallet_page_click", {
      click_name: "repayment_apply_repay_click",
      repayType: "Button 1",
    });
  });
});
