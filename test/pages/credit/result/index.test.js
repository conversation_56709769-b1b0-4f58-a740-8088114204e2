import { jest, describe, beforeEach, afterEach, test, expect } from "vitest";
import { mount } from "@vue/test-utils";
import { ref } from "vue";
import Big from "big.js";
import Index from "../../../../src/pages/credit/result/index.vue";

// 模拟依赖项
jest.mock("big.js");
jest.mock("../../../../src/helpers/native-bridge", () => ({
  goto: jest.fn(),
  report: jest.fn(),
  setWithExactExpireUnit: jest.fn(),
}));

describe("Credit Result Page", () => {
  let wrapper;
  const mockData = {
    value: {
      applyInfo: {
        remainLimit: 10000,
        applyStatus: 2,
        refuseControlDays: 30,
        apr: 10,
        dayRate: 0.027,
      },
      param: {
        applyNo: "12345",
        userId: "user123",
        message: "Test message",
      },
      suggestProducts: [
        { id: 1, name: "Product 1" },
        { id: 2, name: "Product 2" },
      ],
    },
  };

  beforeEach(() => {
    wrapper = mount(Index, {
      global: {
        mocks: {
          data: ref(mockData.value),
          store: {
            param: {
              versionCode: 81300350,
              userId: "user123",
            },
          },
        },
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Computed Properties", () => {
    test("creditLimit should calculate correctly", () => {
      expect(wrapper.vm.creditLimit).toEqual(new Big(100));
    });

    test("applyNo should return correct value", () => {
      expect(wrapper.vm.applyNo).toBe("12345");
    });

    test("applyStatus should return correct status", () => {
      expect(wrapper.vm.applyStatus).toBe(2);
    });

    test("message should return correct message", () => {
      expect(wrapper.vm.message).toBe("Test message");
    });

    test("refuseControlDays should return correct days", () => {
      expect(wrapper.vm.refuseControlDays).toBe(30);
    });

    test("suggestProducts should return correct products", () => {
      expect(wrapper.vm.suggestProducts).toEqual([
        { id: 1, name: "Product 1" },
        { id: 2, name: "Product 2" },
      ]);
    });
  });

  describe("Methods", () => {
    describe("borrow", () => {
      test("should call setUpdateShortcut and report when applyStatus is 2", () => {
        wrapper.vm.borrow();
        expect(wrapper.vm.setUpdateShortcut).toHaveBeenCalled();
        expect(wrapper.vm.report).toHaveBeenCalledWith("wallet_page_click", {
          click_name: "credit_apply_success_finish_click",
        });
        expect(wrapper.vm.setWithExactExpireUnit).toHaveBeenCalledWith(
          "U_HOME_HIDE_SHOW_ANIMATION",
          true,
          7,
          "D",
        );
        expect(wrapper.vm.goto).toHaveBeenCalledWith(
          "/wallet-loan-web/pages/loan/calc?amount=10000&fromPage=creditResult",
        );
      });

      test("should not call report when applyStatus is not 2", () => {
        mockData.value.applyInfo.applyStatus = 1;
        wrapper.vm.borrow();
        expect(wrapper.vm.report).not.toHaveBeenCalled();
      });
    });

    describe("toHome", () => {
      test("should call setUpdateShortcut and goto", () => {
        wrapper.vm.toHome();
        expect(wrapper.vm.setUpdateShortcut).toHaveBeenCalled();
        expect(wrapper.vm.goto).toHaveBeenCalledWith("/wallet-loan-web/pages/index", false, true);
      });

      test("should report when applyStatus is 3", () => {
        mockData.value.applyInfo.applyStatus = 3;
        wrapper.vm.toHome();
        expect(wrapper.vm.report).toHaveBeenCalledWith("wallet_page_click", {
          click_name: "credit_apply_fail_finish_click",
        });
      });
    });

    describe("onClickLeft", () => {
      test("should call goto with correct parameters", () => {
        wrapper.vm.onClickLeft();
        expect(wrapper.vm.goto).toHaveBeenCalledWith("/wallet-loan-web/pages/index", false, true);
      });
    });

    describe("setUpdateShortcut", () => {
      test("should update shortcut when conditions are met", async () => {
        wrapper.vm.showShortCut = false;
        wrapper.vm.updateShortcutChecked = true;

        await wrapper.vm.setUpdateShortcut();

        expect(wrapper.vm.updateShortcut).toHaveBeenCalledWith(true);
      });

      test("should not update shortcut when showShortCut is true", async () => {
        wrapper.vm.showShortCut = true;

        await wrapper.vm.setUpdateShortcut();

        expect(wrapper.vm.updateShortcut).not.toHaveBeenCalled();
      });
    });
  });

  describe("Lifecycle Hooks", () => {
    test("onMounted should set up event listeners", () => {
      expect(window.addEventListener).toHaveBeenCalledWith("online", expect.any(Function));
      expect(window.addEventListener).toHaveBeenCalledWith("hashchange", expect.any(Function));
    });
  });
});
