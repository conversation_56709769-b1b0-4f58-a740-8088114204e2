import { mount } from "@vue/test-utils";
import { createPinia } from "pinia";
import { describe, it, expect, vi, beforeEach, afterEach } from "vitest";
import Index from "../../../../src/pages/credit/sign/index.vue"; // 根据实际路径调整
import {
  initStore,
  upgradeCreditProcessStore,
  contractClickCallback,
} from "../../../../src/helpers/utils"; // 根据实际路径调整
import { report, goto } from "../../../../src/helpers/native-bridge"; // 根据实际路径调整
import { getStore, setWithExactExpireUnit } from "../../../../src/helpers/storage"; // 根据实际路径调整
import { showRetentionDialog } from "../../../../src/helpers/retention-dialog-utils"; // 根据实际路径调整

// 模拟依赖
vi.mock("../../../../src/helpers/utils", () => ({
  initStore: vi.fn(),
  upgradeCreditProcessStore: vi.fn().mockResolvedValue(undefined),
  contractClickCallback: vi.fn().mockReturnValue({ next: "goto", href: "/some-url" }),
}));

vi.mock("../../../../src/helpers/native-bridge", () => ({
  regNativeEvent: vi.fn(),
  goto: vi.fn(),
  report: vi.fn(),
}));

vi.mock("../../../../src/helpers/network-helper", () => ({
  setNetwork: vi.fn(),
}));

vi.mock("../../../../src/helpers/storage", () => ({
  getStore: vi.fn(),
  setWithExactExpireUnit: vi.fn(),
}));

vi.mock("../../../../src/helpers/retention-dialog-utils", () => ({
  showRetentionDialog: vi.fn(),
}));

describe("Index Component", () => {
  let pinia;
  let store;
  let data;

  beforeEach(() => {
    pinia = createPinia();
    store = {
      contractFail: false,
      contractList: vi.fn().mockResolvedValue(undefined),
      goNext: vi.fn().mockResolvedValue(undefined),
      iframeArr: [],
      signLoading: false,
      contractNameList: [],
    };
    data = {
      value: {
        dialogShow: false,
        messageStatus: 0,
        readingSecondsTime: 0,
        param: {
          userId: "someUserId",
          deviceModel: "someDeviceModel",
          fromPage: "someFromPage",
        },
        supplierName: "someSupplierName",
        iframeArr: [],
        currentIndex: 0,
        afterReadingItStatus: false,
        readAll: 0,
        storageDataV2: {},
        dialogMessageOne: "someDialogMessageOne",
        dialogMessageFail: "someDialogMessageFail",
        signLoading: false,
        contractFail: false,
        expendAll: false,
        iframeHeight: "100%",
        contentHeight: "100%",
      },
      dialogShow: false,
      messageStatus: 0,
      readingSecondsTime: 0,
      param: {
        userId: "someUserId",
        deviceModel: "someDeviceModel",
        fromPage: "someFromPage",
      },
      supplierName: "someSupplierName",
      iframeArr: [],
      currentIndex: 0,
      afterReadingItStatus: false,
      readAll: 0,
      storageDataV2: {},
      dialogMessageOne: "someDialogMessageOne",
      dialogMessageFail: "someDialogMessageFail",
      signLoading: false,
      contractFail: false,
      expendAll: false,
      iframeHeight: "100%",
      contentHeight: "100%",
    };

    vi.mocked(initStore).mockReturnValue({ store, data });
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  it("should render correctly", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    console.log("wrapper", wrapper.exists(), wrapper.find(".content-title").text());
    expect(wrapper.exists()).toBe(true);
    expect(wrapper.find(".content-title").text()).toContain("someSupplierName");
  });

  it("should call agreeProtocal and make request", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;
    vm.request = vi.fn();
    vm.agreeProtocal = vi.fn();
    vm.userId = "someUserId";
    vm.signInfo = [
      {
        agrType: "USER_PROTOCAL_CODE",
        country: "CN",
        branchId: 0,
        language: "zh-CN",
        isAgree: true,
      },
      {
        agrType: "PRIVACY_PROTOCAL_CODE",
        country: "CN",
        branchId: 0,
        language: "zh-CN",
        isAgree: true,
      },
    ];
    vm.deviceInfo = {
      deviceId: "",
      deviceModel: "someDeviceModel",
      deviceType: "",
    };
    // Mock the request function
    vi.spyOn(vm, "request").mockResolvedValueOnce({
      message: "success",
    });

    console.log("vm", vm.data);

    // Trigger agreeProtocal
    await vm.agreeProtocal();

    expect(vm.request).toHaveBeenCalledWith("/general/api/ams/user/signAgreement", {
      userId: vm.userId,
      signInfo: vm.signInfo,
      deviceInfo: vm.deviceInfo,
    });

    expect(report).toHaveBeenCalledWith("wallet_page_click", {
      click_name: "credit_another_dialog_continue",
    });
    expect(vm.data.dialogShow).toBe(false);
  });

  it("should handle continueLater", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.continueLater();

    expect(report).toHaveBeenCalledWith("wallet_page_click", {
      click_name: "credit_another_dialog_abandon",
    });
    expect(report).toHaveBeenCalledWith("wallet_page_click", {
      page_name: "credit_agreement_back",
    });
    expect(goto).toHaveBeenCalledWith("/wallet-loan-web/pages/index", false, true);
  });

  it("should handle continueApply", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.data.dialogShow = true;
    vm.data.messageStatus = 2;

    vm.continueApply();

    expect(report).toHaveBeenCalledWith("wallet_page_click", {
      click_name: "credit_another_dialog_continue",
    });
    expect(vm.data.dialogShow).toBe(false);
    expect(vm.data.readingSecondsTime).toBe(vm.data.readingSeconds);
  });

  it("should handle dialogJumpTo with retainUser", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.data.storageDataV2.retainUser = true;

    vm.dialogJumpTo();

    expect(vm.continueLater).toHaveBeenCalled();
  });

  it("should handle dialogJumpTo without retainUser", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.data.storageDataV2.retainUser = false;

    vm.dialogJumpTo();

    expect(showRetentionDialog).toHaveBeenCalledWith({
      text: vm.data.dialogMessageOne,
      cancelFunc: expect.any(Function),
    });
    expect(vm.data.messageStatus).toBe(1);
    expect(setWithExactExpireUnit).toHaveBeenCalledWith(
      `U_CREDIT_PROCESS_V2_${vm.data.param.userId}`,
      vm.data.storageDataV2,
      7,
      "D",
    );
  });

  it("should handle onResume", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    getStore.mockReturnValue({
      verifyList: ["step1", "step2"],
      step1: true,
      step2: false,
    });

    vm.onResume();

    expect(getStore).toHaveBeenCalledWith(`U_CREDIT_PROCESS_V2_${vm.data.param.userId}`);
    expect(vm.data.dialogMessageOne).toBe("仅差1步即可完成申请，确定离开？");
    expect(vm.data.messageStatus).toBe(2);
    expect(vm.data.dialogShow).toBe(true);
    expect(report).toHaveBeenCalledWith("wallet_page_view", {
      page_name: "credit_another_dialog_show",
    });
  });

  it("should handle afterReadingIt", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.data.iframeArr = [{ status: true }, { status: true }];

    vm.afterReadingIt();

    expect(vm.data.afterReadingItStatus).toBe(true);
    expect(setWithExactExpireUnit).toHaveBeenCalledWith(
      `U_CREDIT_PROCESS_V2_${vm.data.param.userId}`,
      vm.data.storageDataV2,
      7,
      "D",
    );
  });

  it("should handle allReaded", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    await vm.allReaded();

    expect(goLivingBodyFun).toHaveBeenCalledWith(true);
    expect(store.goNext).toHaveBeenCalled();
  });

  it("should handle iframeClick", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.data.iframeArr = [
      { contractUrl: "http://example.com/contract1.pdf", status: false },
      { contractUrl: "http://example.com/contract2.pdf", status: false },
    ];

    await vm.iframeClick(0);

    expect(ifReading.value[0]).toBe(true);
    expect(vm.data.currentIndex).toBe(0);
    expect(iframeRef.value.src).toBe("http://example.com/contract1.pdf");
  });

  it("should handle handleTouchStart", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    const event = {
      touches: [{ clientY: 100 }],
    };

    vm.handleTouchStart(event);

    expect(startY).toBe(100);
  });

  it("should handle swipeTop", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    scrollPosition.value = {
      st: 0,
      ed: 0,
      top: 0,
      bottom: 0,
      init: "top",
    };

    vm.swipeTop();

    expect(vm.data.iframeArr[0].status).toBe(true);
    expect(iframeClick).toHaveBeenCalledWith(-1, "bottom");
    expect(afterReadingIt).toHaveBeenCalled();
  });

  it("should handle swipeBottom", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    scrollPosition.value = {
      st: 100,
      ed: 100,
      top: 0,
      bottom: 100,
      init: "top",
    };

    vm.swipeBottom();

    expect(vm.data.iframeArr[0].status).toBe(true);
    expect(iframeClick).toHaveBeenCalledWith(1, "top");
    expect(afterReadingIt).toHaveBeenCalled();
  });

  it("should handle handleScroll", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    const event = {
      touches: [{ clientY: 50 }],
    };

    vm.handleScroll(event);

    expect(scrollPosition.value.ed).toBe(0);
  });

  it("should handle handleClick", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    const event = {};

    vm.handleClick(event);

    expect(contractClickCallback).toHaveBeenCalledWith(event);
    expect(goto).toHaveBeenCalledWith("/some-url", true);
  });

  it("should handle handleIframeLoad", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    iframeRef.value = {
      contentDocument: {
        documentElement: {
          scrollHeight: 200,
        },
        defaultView: {
          innerHeight: 100,
        },
      },
    };

    vm.handleIframeLoad();

    expect(scrollPosition.value).toEqual({
      st: 0,
      ed: 0,
      top: 0,
      bottom: 100,
      init: "top",
    });
  });

  it("should handle changeList", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.changeList();

    expect(isShowMore.value).toBe(!isShowMore.value);
  });

  it("should compute getIframeArr", () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.data.iframeArr = [
      { contractUrl: "http://example.com/contract1.pdf" },
      { contractUrl: "http://example.com/contract2.pdf" },
    ];

    data.iframeArr = [
      { contractUrl: "http://example.com/contract1.pdf" },
      { contractUrl: "http://example.com/contract2.pdf" },
    ];

    expect(vm.getIframeArr).toEqual([{ contractUrl: "http://example.com/contract1.pdf" }]);

    isShowMore.value = true;

    expect(vm.getIframeArr).toEqual([
      { contractUrl: "http://example.com/contract1.pdf" },
      { contractUrl: "http://example.com/contract2.pdf" },
    ]);
  });

  it("should watch currentIndex", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.data.currentIndex = 1;

    await vm.$nextTick();

    expect(handleIframeLoad).toHaveBeenCalled();
  });

  it("should watch isShowMore", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    isShowMore.value = true;

    await vm.$nextTick();

    expect(vm.data.iframeHeight).toBe(`${window.innerHeight - 88 - 25 - 86 - 248}px`);

    isShowMore.value = false;

    await vm.$nextTick();

    expect(vm.data.iframeHeight).toBe(`${window.innerHeight - 88 - 25 - 86 - 24}px`);
  });

  it("should watch iframeArr", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    vm.data.iframeArr = [{ contractUrl: "http://example.com/contract1.pdf" }];

    await vm.$nextTick();

    expect(store.iframeArr[0].contractName).toBeUndefined();
    expect(store.iframeArr[0].status).toBeUndefined();

    store.iframeArr[0] = {
      contractName: "Contract 1",
      status: true,
    };

    expect(store.signLoading).toBe(false);
  });

  it("should handle onMounted", async () => {
    const wrapper = mount(Index, {
      global: {
        plugins: [pinia],
      },
    });
    const { vm } = wrapper;

    await vm.$nextTick();

    expect(upgradeCreditProcessStore).toHaveBeenCalledWith(
      vm.data.param.userId,
      "AGREEMENT_CREDIT",
    );
    expect(goLivingBodyFun).toHaveBeenCalledWith(false);
    expect(onResume).toHaveBeenCalled();
    expect(store.contractList).toHaveBeenCalled();
    expect(isShowMore.value).toBe(store.expendAll || isShowMore.value);
  });
});
