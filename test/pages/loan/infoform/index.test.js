import { jest, describe, test, expect, beforeEach } from "vitest";
import { mount } from "@vue/test-utils";
import { ref, nextTick } from "vue";
import Index from "../../../../src/pages/loan/infoform/index.vue";
import useStore from "../../../../src/pages/loan/infoform/store";
import { initStore } from "../../../../src/helpers/utils";
import { regNativeEvent, enableBackPress } from "../../../../src/helpers/native-bridge";

// Mock dependencies
jest.mock("./store", () => ({
  useStore: jest.fn().mockReturnValue({
    param: {},
    data: {},
  }),
}));
jest.mock("../../../../src/helpers/utils", () => ({
  initStore: jest.fn().mockReturnValue({ store: {}, data: {} }),
  request: jest.fn(),
}));
jest.mock("../../../../src/helpers/native-bridge", () => ({
  goto: jest.fn(),
  report: jest.fn(),
  regNativeEvent: jest.fn(),
  enableBackPress: jest.fn(),
}));
jest.mock("../../../../src/helpers/network-helper", () => ({
  setNetwork: jest.fn(),
}));
jest.mock("../../../../src/helpers/storage", () => ({
  getStore: jest.fn().mockReturnValue({}),
  setWithExactExpireUnit: jest.fn(),
}));
jest.mock("../../../../src/helpers/next", () => ({
  loanNext: jest.fn(),
}));
jest.mock("../../../../src/helpers/retention-dialog-utils", () => ({
  showRetentionDialog: jest.fn(),
}));

describe("InfoForm Page", () => {
  let wrapper;
  const mockStore = {
    param: {},
    data: {
      infoFileds: [],
      privacyNotice: "请保护您的隐私信息",
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    useStore.mockReturnValue(mockStore);
    initStore.mockReturnValue({ store: mockStore, data: ref(mockStore.data) });
    wrapper = mount(Index, {
      global: {
        components: {},
      },
    });
  });

  describe("Rendering", () => {
    test("should not render content-tip if infoFileds is empty", () => {
      expect(wrapper.find(".content-tip").exists()).toBe(false);
    });

    test("should render content-tip if infoFileds is not empty", async () => {
      mockStore.data.infoFileds = ["field1"];
      await nextTick();
      expect(wrapper.find(".content-tip").exists()).toBe(true);
      expect(wrapper.find(".content-tip span.icon-confidentiality").exists()).toBe(true);
      expect(wrapper.find(".content-tip span").text()).toContain("请保护您的隐私信息");
    });
  });

  describe("Refs Initialization", () => {
    test("should initialize refs correctly", () => {
      expect(wrapper.vm.containerRef).toBeNull();
      expect(wrapper.vm.main).toBeNull();
      expect(wrapper.vm.contentParRef).toBeNull();
      expect(wrapper.vm.contentRef).toBeNull();
    });
  });

  describe("Methods", () => {
    describe("handleTouchStart", () => {
      test("should set touchStartTime on touchstart event", async () => {
        const touchEvent = new TouchEvent("touchstart", {
          touches: [{ identifier: 1, clientX: 0, clientY: 0 }],
        });
        wrapper.element.dispatchEvent(touchEvent);
        await nextTick();
        expect(wrapper.vm.touchStartTime).toBeGreaterThan(0);
      });
    });

    describe("handleTouchEnd", () => {
      test("should calculate swipe distance and handle accordingly on touchend event", async () => {
        wrapper.vm.touchStartTime = Date.now() - 100;
        const touchEvent = new TouchEvent("touchend", {
          changedTouches: [{ identifier: 1, clientX: 100, clientY: 0 }],
        });
        wrapper.element.dispatchEvent(touchEvent);
        await nextTick();
        // Assuming some logic to handle swipe here
        // For now, we just check that the method was called
        expect(wrapper.vm.handleTouchEnd).toHaveBeenCalled();
      });
    });
  });

  describe("Lifecycle Hooks", () => {
    describe("onMounted", () => {
      test("should register native events and enable back press", async () => {
        await nextTick();
        expect(regNativeEvent).toHaveBeenCalledWith("onBack", expect.any(Function));
        expect(enableBackPress).toHaveBeenCalledWith(false);
      });
    });

    describe("onUnmounted", () => {
      test("should unregister native events", async () => {
        await nextTick();
        wrapper.unmount();
        expect(regNativeEvent).toHaveBeenCalledWith("onBack", expect.any(Function), true);
      });
    });

    describe("onServerPrefetch", () => {
      test("should call store.initial on server prefetch", () => {
        expect(initStore).toHaveBeenCalledWith(useStore);
        expect(mockStore.initial).toHaveBeenCalled();
      });
    });
  });

  describe("Watchers", () => {
    test("should watch contentRef and calculate contentHeight", async () => {
      const mockContentRef = { offsetHeight: 500 };
      wrapper.vm.contentRef = mockContentRef;
      await nextTick();
      expect(wrapper.vm.contentHeight).toBe("500px");
    });
  });
});
