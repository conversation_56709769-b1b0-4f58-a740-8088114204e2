import { jest, describe, test, expect, beforeEach } from "vitest";
import { mount } from "@vue/test-utils";
import { ref } from "vue";
import Index from "../../../../src/pages/loan/sign/index.vue";

// 模拟依赖项
jest.mock("../../../../src/helpers/native-bridge", () => ({
  goto: jest.fn(),
  back: jest.fn(),
  report: jest.fn(),
  regNativeEvent: jest.fn(),
  enableBackPress: jest.fn(),
}));

jest.mock("../../../../src/helpers/network-helper", () => ({
  setNetwork: jest.fn(),
}));

jest.mock("../../../../src/helpers/utils", () => ({
  initStore: jest.fn(),
  request: jest.fn(),
  contractClickCallback: jest.fn(),
}));

describe("Loan Sign Page", () => {
  let wrapper;
  const mockStore = {
    info: {},
    displayInfo: {},
    supplierName: "测试供应商",
    contractNameList: ["借款协议", "隐私协议"],
    iframeArr: [
      {
        status: false,
        then: jest.fn().mockImplementation((callback) => {
          callback({ contractType: "html", contractUrl: "https://example.com/contract1" });
        }),
      },
      {
        status: false,
        then: jest.fn().mockImplementation((callback) => {
          callback({ contractType: "html", contractUrl: "https://example.com/contract2" });
        }),
      },
    ],
    getContractList: jest.fn(),
    loading: false,
    signLoading: false,
    contractFail: false,
    backDisabled: false,
  };

  beforeEach(() => {
    wrapper = mount(Index, {
      global: {
        mocks: {
          data: ref({
            iframeArr: mockStore.iframeArr,
            currentIndex: 0,
            afterReadingItStatus: false,
            loading: false,
            signLoading: false,
            contractFail: false,
            param: { userId: "user123" },
            signInfo: { readAll: false, readingSeconds: 10 },
          }),
          store: mockStore,
        },
      },
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe("Lifecycle Hooks", () => {
    test("onMounted should initialize page", async () => {
      expect(mockStore.getContractList).toHaveBeenCalled();
      expect(wrapper.vm.reportList).toEqual([]);
    });
  });

  describe("Methods", () => {
    describe("onClickLeft", () => {
      test("should show retention dialog when back button clicked", () => {
        wrapper.vm.onClickLeft();
        expect(wrapper.vm.reportList).toContain("onClickLeft");
      });
    });

    describe("iframeClick", () => {
      test("should load iframe content when agreement clicked", async () => {
        await wrapper.vm.iframeClick(0);
        expect(wrapper.vm.data.currentIndex).toBe(0);
        expect(wrapper.vm.ifReading[0]).toBe(true);
      });
    });

    describe("agreeClick", () => {
      test("should call loanNext when agree button clicked", async () => {
        wrapper.vm.data.afterReadingItStatus = true;
        await wrapper.vm.agreeClick();
        expect(wrapper.vm.report).toHaveBeenCalledWith("wallet_page_click", {
          click_name: "loan_agreement_read_click",
        });
        expect(wrapper.vm.data.loading).toBe(true);
      });
    });

    describe("reload", () => {
      test("should reload contract list when network is offline", () => {
        window.navigator.onLine = false;
        wrapper.vm.reload();
        expect(wrapper.vm.setNetwork).toHaveBeenCalled();
      });

      test("should reload contract list when contract load failed", () => {
        window.navigator.onLine = true;
        wrapper.vm.store.contractFail = true;
        wrapper.vm.reload();
        expect(wrapper.vm.store.getContractList).toHaveBeenCalled();
      });
    });
  });

  describe("UI Interactions", () => {
    test("clicking agreement item should load iframe", async () => {
      const agreementItems = wrapper.findAll(".btn-area-agreement");
      await agreementItems[0].trigger("click");
      expect(wrapper.vm.iframeClick).toHaveBeenCalled();
    });

    test("clicking expand/collapse button should toggle list", async () => {
      const expandButton = wrapper.find(".header");
      await expandButton.trigger("click");
      expect(wrapper.vm.isShowMore).toBe(true);
      await expandButton.trigger("click");
      expect(wrapper.vm.isShowMore).toBe(false);
    });
  });

  describe("Computed Properties", () => {
    test("getIframeArr should return correct array based on isShowMore", () => {
      expect(wrapper.vm.getIframeArr().length).toBe(1);
      wrapper.vm.isShowMore = true;
      expect(wrapper.vm.getIframeArr().length).toBe(2);
    });
  });

  describe("Watch Effects", () => {
    test("iframeArr watch should initialize first iframe", async () => {
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.iframeClick).toHaveBeenCalledWith(0);
    });

    test("isShowMore watch should update iframe height", async () => {
      const originalHeight = wrapper.vm.iframeHeight;
      wrapper.vm.isShowMore = true;
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.iframeHeight).not.toBe(originalHeight);
    });
  });
});
