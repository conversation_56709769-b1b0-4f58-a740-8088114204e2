import { jest, describe, test, expect, beforeEach } from "vitest";
import { mount } from "@vue/test-utils";
import { ref, nextTick } from "vue";
import Index from "../../../../src/pages/loan/result/index.vue";
import { initStore } from "../../../../src/helpers/utils";
import {
  goto,
  report,
  updateShortcut,
  enableBackPress,
  regNativeEvent,
} from "../../../../src/helpers/native-bridge";
import { setWithExactExpireUnit, del } from "../../../../src/helpers/storage";
import SuggestProductCard from "../../../../src/pages/component/SuggestProductCard.vue";
import IcsvgPublicBackFilled from "../../../../src/components/svg/icsvgPublicBackFilled.vue";
import resourcePosition from "../../../../src/components/resourcePosition.vue";

// Mock dependencies
jest.mock("../../../../src/helpers/utils", () => ({
  initStore: jest.fn().mockReturnValue({ store: {}, data: {} }),
  request: jest.fn(),
  amountNumberFormat: jest.fn((amount) => amount),
}));
jest.mock("../../../../src/helpers/native-bridge", () => ({
  goto: jest.fn(),
  report: jest.fn(),
  hasShortcut: jest.fn().mockResolvedValue(false),
  updateShortcut: jest.fn().mockResolvedValue(true),
  enableBackPress: jest.fn(),
  regNativeEvent: jest.fn(),
  decrypt: jest.fn().mockResolvedValue({}),
}));
jest.mock("../../../../src/helpers/network-helper", () => ({
  setNetwork: jest.fn(),
}));
jest.mock("../../../../src/helpers/storage", () => ({
  getStore: jest.fn().mockReturnValue({}),
  setWithExactExpireUnit: jest.fn(),
  del: jest.fn(),
}));
jest.mock("../../../../src/helpers/next", () => ({
  loanNext: jest.fn(),
}));

describe("Loan Result Page", () => {
  let wrapper;
  const mockStore = {
    initial: jest.fn(),
    queryApplyStatus: jest.fn(),
    getResourcePositionInfo: jest.fn(),
    param: {
      versionCode: 81300350,
      userId: "testUserId",
      applyExpired: false,
    },
    applyRes: {
      applyStatus: 1,
      totalAmount: 10000,
      firstTermAmount: 3000,
      repayDay: "2023-10-15",
    },
    curOrderInvalid: false,
    signStatus: 0,
    suggestProducts: [],
    isOnResume: false,
  };
  const mockData = {
    errorCode: null,
    errorMessage: "",
    param: {
      applyStatus: 1,
    },
    applyRes: {
      applyStatus: 1,
      totalAmount: 10000,
      firstTermAmount: 3000,
      repayDay: "2023-10-15",
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    initStore.mockReturnValue({ store: mockStore, data: ref(mockData) });
    wrapper = mount(Index, {
      global: {
        components: {
          SuggestProductCard,
          IcsvgPublicBackFilled,
          resourcePosition,
        },
      },
    });
  });

  describe("Rendering", () => {
    test("should render loading state initially", () => {
      expect(wrapper.find(".circleStyle img").attributes("alt")).toBe("");
      expect(wrapper.find(".textStyle1").text()).toBe("正在快速处理您的申请");
    });

    test("should render success state after applying successfully", async () => {
      mockStore.applyRes.applyStatus = 2;
      mockData.applyRes.applyStatus = 2;
      await nextTick();
      expect(wrapper.find(".circleStyle img").attributes("alt")).toBe("");
      expect(wrapper.find(".textStyle1").text()).toBe("借款发放成功");
      expect(wrapper.find(".limit-money").text()).toBe("￥10000");
    });

    test("should render failure state after applying fails", async () => {
      mockStore.applyRes.applyStatus = 3;
      mockData.applyRes.applyStatus = 3;
      await nextTick();
      expect(wrapper.find(".circleStyle img").attributes("alt")).toBe("");
      expect(wrapper.find(".textStyle1").text()).toBe("申请失败");
    });

    test("should render expired order guide page", async () => {
      mockStore.param.applyExpired = true;
      await nextTick();
      expect(wrapper.find(".textStyle1").text()).toBe("借款发放成功");
    });

    test("should render network error message", async () => {
      mockData.applyFlag = true;
      mockData.errorMessage = "Network error";
      await nextTick();
      expect(wrapper.find(".textStyle1").text()).toBe("网络未连接，请检查网络设置");
    });
  });

  describe("Methods", () => {
    describe("loanExpiredClick", () => {
      test("should navigate to loan calculation page on confirm", async () => {
        await wrapper.vm.loanExpiredClick("confirm");
        expect(goto).toHaveBeenCalledWith(
          "/wallet-loan-web/pages/calc?amount=0&fromPage=loanResult",
          false,
          true,
        );
      });

      test("should navigate to home page on cancel", async () => {
        await wrapper.vm.loanExpiredClick("cancel");
        expect(del).toHaveBeenCalledWith("U_LOAN_PROCESS_testUserId");
        expect(goto).toHaveBeenCalledWith("/wallet-loan-web/pages/index", false, true);
      });
    });

    describe("filterFailTitle", () => {
      test('should return "申请失败" when applyFlag is true', () => {
        mockStore.applyFlag = true;
        expect(wrapper.vm.filterFailTitle).toBe("申请失败");
      });

      test('should return "订单失效，请重新申请" when curOrderInvalid is true', () => {
        mockStore.curOrderInvalid = true;
        expect(wrapper.vm.filterFailTitle).toBe("订单失效，请重新申请");
      });

      test('should return "暂无法申请借款" otherwise', () => {
        mockStore.applyFlag = false;
        mockStore.curOrderInvalid = false;
        expect(wrapper.vm.filterFailTitle).toBe("暂无法申请借款");
      });
    });

    describe("loanAmount and firstTermAmount", () => {
      test("should format loanAmount correctly", () => {
        expect(wrapper.vm.loanAmount).toBe("10000");
      });

      test("should format firstTermAmount correctly", () => {
        expect(wrapper.vm.firstTermAmount).toBe("3000");
      });
    });

    describe("addShortCut", () => {
      test("should add shortcut if conditions met", async () => {
        await wrapper.vm.addShortCut();
        expect(updateShortcut).toHaveBeenCalledWith(true);
        expect(del).toHaveBeenCalledWith("U_POPUP_STORE_testUserId");
      });

      test("should not add shortcut if conditions not met", async () => {
        mockStore.param.versionCode = 81300349;
        await wrapper.vm.addShortCut();
        expect(updateShortcut).not.toHaveBeenCalled();
      });
    });

    describe("toHomeClick", () => {
      test("should report events based on applyStatus", async () => {
        mockStore.applyRes.applyStatus = 2;
        await wrapper.vm.toHomeClick();
        expect(report).toHaveBeenCalledWith("wallet_page_click", {
          click_name: "loan_apply_success_finish_click",
        });
      });

      test("should setWithExactExpireUnit for successInfo", async () => {
        mockStore.applyRes.applyStatus = 2;
        await wrapper.vm.toHomeClick();
        expect(setWithExactExpireUnit).toHaveBeenCalledWith(
          "U_HOME_HIDE_SHOW_ANIMATION",
          true,
          7,
          "D",
        );
      });
    });

    describe("goBack", () => {
      test("should navigate to home page if curOrderInvalid is false", async () => {
        await wrapper.vm.goBack();
        expect(goto).toHaveBeenCalledWith("/wallet-loan-web/pages/index", false, true);
      });

      test("should call loanExpiredClick on cancel if curOrderInvalid is true", async () => {
        mockStore.curOrderInvalid = true;
        await wrapper.vm.goBack();
        expect(wrapper.vm.loanExpiredClick).toHaveBeenCalledWith("cancel");
      });
    });

    describe("filterVerifyList", () => {
      test("should filter verifyList correctly", () => {
        const list = ["FACE_CHECK", "AGREEMENT_COMMON", "SMS"];
        const result = wrapper.vm.filterVerifyList(list);
        expect(result.steps).toEqual(["FACE_CHECK", "AGREEMENT_P", "SMS"]);
        expect(result.infoFileds).toEqual([]);
      });
    });

    describe("getReapplyDate", () => {
      test("should return correct reapply date", () => {
        const date = wrapper.vm.getReapplyDate(5);
        expect(date).toMatch(/\d+月\d+日/);
      });
    });

    describe("getControlDay", () => {
      test("should return control day message based on conditions", () => {
        expect(wrapper.vm.getControlDay()).toBe("请重试");
      });
    });

    describe("handleNetwork", () => {
      test("should reload the page if not in review or no network issue", () => {
        wrapper.vm.handleNetwork();
        expect(window.location.reload).toHaveBeenCalled();
      });
    });

    describe("filterReportEvent", () => {
      test("should report events based on applyStatus", () => {
        wrapper.vm.filterReportEvent(2);
        expect(report).toHaveBeenCalledWith("wallet_page_view", {
          page_name: "loan_apply_success_page",
        });
      });
    });
  });

  describe("Lifecycle Hooks", () => {
    describe("onMounted", () => {
      test("should initialize component state and register native events", async () => {
        await nextTick();
        expect(initStore).toHaveBeenCalled();
        expect(enableBackPress).toHaveBeenCalledWith(false);
        expect(regNativeEvent).toHaveBeenCalledWith("onBack", expect.any(Function));
        expect(regNativeEvent).toHaveBeenCalledWith("onResume", expect.any(Function));
      });

      test("should handle expired order", async () => {
        mockStore.param.applyExpired = true;
        await nextTick();
        expect(wrapper.vm.showLoanExpiredGuidePage).toHaveBeenCalled();
        expect(enableBackPress).toHaveBeenCalledWith(true);
      });

      test("should handle network errors", async () => {
        mockData.applyFlag = true;
        mockData.errorMessage = "Network error";
        await nextTick();
        expect(flag.value).toBe(true);
        expect(enableBackPress).toHaveBeenCalledWith(true);
      });

      test("should handle successful application", async () => {
        mockData.param.applyStatus = 2;
        await nextTick();
        expect(flag.value).toBe(true);
        expect(enableBackPress).toHaveBeenCalledWith(true);
      });

      test("should handle offline scenario", async () => {
        Object.defineProperty(navigator, "onLine", { value: false, writable: true });
        await nextTick();
        expect(flag.value).toBe(true);
        expect(enableBackPress).toHaveBeenCalledWith(true);
      });

      test("should handle other scenarios", async () => {
        mockData.param.applyCode = "someCode";
        await nextTick();
        expect(flag.value).toBe(true);
        expect(enableBackPress).toHaveBeenCalledWith(true);
      });

      test("should poll for application status", async () => {
        jest.useFakeTimers();
        await nextTick();
        jest.runOnlyPendingTimers();
        expect(mockStore.queryApplyStatus).toHaveBeenCalled();
        jest.runOnlyPendingTimers();
        expect(mockStore.queryApplyStatus).toHaveBeenCalledTimes(2);
        jest.useRealTimers();
      });
    });
  });
});
