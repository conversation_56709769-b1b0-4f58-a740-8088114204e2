import { describe, it, expect, vi, beforeEach } from "vitest";
import {
  getCurrentCpInfo,
  delCurrentCpInfo,
  getClientConfigInfo,
  storePopupConfigList,
  getPopupConfigList,
  getActivityPopupConfigList,
  getRetentionPopupConfigList,
  delPopupConfigList,
  delRetentionStatusStore,
  determineReActiveStatus,
  processEvent,
} from "../../src/helpers/configUtils";
import * as storage from "../../src/helpers/storage";
import * as configApi from "../../src/api/config";
import * as adApi from "../../src/api/ad";
import * as constants from "../../src/helpers/constants";

// Mock dependencies
vi.mock("../../src/helpers/storage");
vi.mock("../../src/api/config");
vi.mock("../../src/api/ad");
vi.mock("../../src/helpers/constants");

// Mock configUtils with specific functions
vi.mock("../../src/helpers/configUtils", async (importOriginal) => {
  const original = await importOriginal();
  return {
    ...original,
    getPopupConfigList: vi.fn(),
    getCurrentCpInfo: vi.fn(),
    determineReActiveStatus: vi.fn(),
    getActivityPopupConfigList: vi.fn().mockImplementation(async () => ({})),
    getRetentionPopupConfigList: vi.fn().mockImplementation(async () => [])
  };
});

describe("configUtils", () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Mock window.location
    delete window.location;
    window.location = { pathname: "/wallet-loan-web/pages/index" };
    
    // Setup mock implementations with default return values
    getPopupConfigList.mockReset().mockResolvedValue({
      ACTIVITY_POPUP_CONFIG: {},
      RETENTION_POPUP_CONFIG: []
    });
    
    getCurrentCpInfo.mockReset().mockResolvedValue({
      id: "cp-123",
      name: "Test CP"
    });
    
    determineReActiveStatus.mockReset().mockReturnValue(1);
    
    // Setup specific mocks for helper functions
    getActivityPopupConfigList.mockImplementation(async () => ({}));
    getRetentionPopupConfigList.mockImplementation(async () => []);
  });

  describe("getCurrentCpInfo", () => {
    it("should return cached cpInfo when available", async () => {
      const mockUserId = "test-user";
      const mockCpInfo = { id: "cp-123", name: "Test CP" };
      storage.getStore.mockImplementation((key) => {
        if (key === `U_CPINFO_${constants.STORE_VERSION_CODE}_${mockUserId}`) {
          return mockCpInfo;
        }
        return null;
      });
      constants.getCurrentStore.mockReturnValue({ param: { userId: mockUserId } });

      const result = await getCurrentCpInfo();
      expect(result).toEqual(mockCpInfo);
      expect(configApi.getCpConfig).not.toHaveBeenCalled();
    });

    it("should fetch and cache cpInfo when not cached", async () => {
      const mockUserId = "test-user";
      const mockResponse = { data: { id: "cp-123", name: "Test CP" } };
      storage.getStore.mockReturnValue(null);
      constants.getCurrentStore.mockReturnValue({ param: { userId: mockUserId } });
      configApi.getCpConfig.mockResolvedValue(mockResponse);

      const result = await getCurrentCpInfo();
      expect(result).toEqual(mockResponse.data);
      expect(storage.setWithExactExpire).toHaveBeenCalledWith(
        `U_CPINFO_${constants.STORE_VERSION_CODE}_${mockUserId}`,
        mockResponse.data,
        "1H",
      );
    });

    it("should return null when userId is not available", async () => {
      constants.getCurrentStore.mockReturnValue({ param: {} });
      const result = await getCurrentCpInfo();
      expect(result).toBeNull();
    });
  });

  describe("delCurrentCpInfo", () => {
    it("should delete cpInfo cache", () => {
      const mockUserId = "test-user";
      constants.getCurrentStore.mockReturnValue({ param: { userId: mockUserId } });
      delCurrentCpInfo();
      expect(storage.del).toHaveBeenCalledWith(
        `U_CPINFO_${constants.STORE_VERSION_CODE}_${mockUserId}`,
      );
    });
  });

  describe("getClientConfigInfo", () => {
    it("should return cached config when available", async () => {
      const mockUserId = "test-user";
      const mockConfig = { theme: "dark", language: "en" };
      storage.getStore.mockImplementation((key) => {
        if (key === `U_CLIENT_CONFIG_INFO_${constants.STORE_VERSION_CODE}_${mockUserId}`) {
          return mockConfig;
        }
        return null;
      });
      constants.getCurrentStore.mockReturnValue({ param: { userId: mockUserId } });

      const result = await getClientConfigInfo();
      expect(result).toEqual(mockConfig);
      expect(configApi.getClientConfig).not.toHaveBeenCalled();
    });

    it("should fetch and cache config when not cached", async () => {
      const mockUserId = "test-user";
      const mockResponse = { data: { theme: "dark", language: "en" } };
      storage.getStore.mockReturnValue(null);
      constants.getCurrentStore.mockReturnValue({ param: { userId: mockUserId } });
      configApi.getClientConfig.mockResolvedValue(mockResponse);

      const result = await getClientConfigInfo();
      expect(result).toEqual(mockResponse.data);
      expect(storage.setWithExactExpire).toHaveBeenCalledWith(
        `U_CLIENT_CONFIG_INFO_${constants.STORE_VERSION_CODE}_${mockUserId}`,
        mockResponse.data,
        "1H",
      );
    });
  });

  describe("popup config functions", () => {
    const mockAdSpace = {
      code: 0,
      data: {
        "popup-1": {
          spaceType: 3,
          carousel: 1,
          popupPage: "credit_realname|loan_result",
          popupMoment: 1,
          activityList: [
            {
              id: "act-1",
              picUrl: "https://example.com/image.jpg",
              targetUrl: "https://example.com",
              activityName: "Test Activity",
              abTestPolicy: { expConfCode: "test-policy" },
              popupTriggers: 1,
            },
          ],
        },
      },
    };

    beforeEach(() => {
      constants.urlMap = {
        credit_realname: "/wallet-loan-web/pages/credit/realname",
        loan_result: "/wallet-loan-web/pages/loan/result",
      };
    });

    describe("storePopupConfigList", () => {
      it("should store popup config when response is valid", () => {
        storePopupConfigList(mockAdSpace);
        expect(storage.setWithExactExpire).toHaveBeenCalledTimes(2);
      });

      it('should store "Unavailable" when response is 404', () => {
        storePopupConfigList({ agwError: 1, agwErrorCode: 404 });
        expect(storage.setWithExactExpire).toHaveBeenCalledWith(
          `U_ACTIVITY_POPUP_CONFIG_${constants.STORE_VERSION_CODE}`,
          "Unavailable",
          "1H",
        );
      });

      it("should do nothing when response is invalid", () => {
        storePopupConfigList({ code: 1 });
        expect(storage.setWithExactExpire).not.toHaveBeenCalled();
      });
    });

    describe("getPopupConfigList", () => {
      it("should return cached popup config when available", async () => {
        const mockConfig = {
          ACTIVITY_POPUP_CONFIG: { credit_realname: { pageUrl: "/credit/realname" } },
          RETENTION_POPUP_CONFIG: [],
        };
        storage.getStore.mockImplementation((key) => {
          if (key.includes("ACTIVITY_POPUP_CONFIG")) return mockConfig.ACTIVITY_POPUP_CONFIG;
          if (key.includes("RETENTION_POPUP_CONFIG")) return mockConfig.RETENTION_POPUP_CONFIG;
          return null;
        });

        const result = await getPopupConfigList();
        expect(result).toEqual(mockConfig);
        expect(adApi.getAdActivityListV2).not.toHaveBeenCalled();
      });

      it("should fetch and store popup config when cache is empty", async () => {
        storage.getStore.mockReturnValue(null);
        adApi.getAdActivityListV2.mockResolvedValue(mockAdSpace);

        await getPopupConfigList();
        expect(adApi.getAdActivityListV2).toHaveBeenCalled();
        expect(storage.setWithExactExpire).toHaveBeenCalled();
      });
    });

    describe("getActivityPopupConfigList", () => {
      it("should return activity popup config only", async () => {
        const mockConfig = { ACTIVITY_POPUP_CONFIG: { credit_realname: {} } };
        getPopupConfigList.mockResolvedValue(mockConfig);
        const result = await getActivityPopupConfigList();
        expect(result).toEqual(mockConfig.ACTIVITY_POPUP_CONFIG);
      });
    });

    describe("getRetentionPopupConfigList", () => {
      it("should return retention popup config only", async () => {
        const mockConfig = { RETENTION_POPUP_CONFIG: [] };
        getPopupConfigList.mockResolvedValue(mockConfig);
        const result = await getRetentionPopupConfigList();
        expect(result).toEqual(mockConfig.RETENTION_POPUP_CONFIG);
      });
    });

    describe("delPopupConfigList", () => {
      it("should delete all popup configs", () => {
        delPopupConfigList();
        expect(storage.del).toHaveBeenCalledTimes(2);
      });
    });

    describe("delRetentionStatusStore", () => {
      it("should delete retention status store", () => {
        const mockUserId = "test-user";
        constants.getCurrentStore.mockReturnValue({ param: { userId: mockUserId } });
        delRetentionStatusStore();
        expect(storage.del).toHaveBeenCalledWith(`U_RETENTION_STATUS_${mockUserId}`);
      });
    });
  });

  describe("determineReActiveStatus", () => {
    it("should return 1 when creditInfo status is 3 and cancelStatus is 0", () => {
      window.location.pathname = "/wallet-loan-web/pages/index";
      constants.getCurrentStore.mockReturnValue({
        param: { userId: "test-user" },
        creditInfo: { status: 3, cancelStatus: 0 },
      });
      const result = determineReActiveStatus();
      expect(result).toBe(1);
    });

    it("should return reActiveStatus from creditProcess on credit pages", () => {
      window.location.pathname = "/wallet-loan-web/pages/credit/sign";
      const mockUserId = "test-user";
      constants.getCurrentStore.mockReturnValue({ param: { userId: mockUserId } });
      storage.getStore.mockImplementation((key) => {
        if (key === `U_CREDIT_PROCESS_V2_${mockUserId}`) return { reActive: 1 };
        return null;
      });
      const result = determineReActiveStatus();
      expect(result).toBe(1);
    });

    it("should return reActiveStatus from loanProcess on loan pages", () => {
      window.location.pathname = "/wallet-loan-web/pages/loan/infoform";
      const mockUserId = "test-user";
      constants.getCurrentStore.mockReturnValue({ param: { userId: mockUserId } });
      storage.getStore.mockImplementation((key) => {
        if (key === `U_LOAN_PROCESS_${mockUserId}`) return { reActive: 1 };
        return null;
      });
      const result = determineReActiveStatus();
      expect(result).toBe(1);
    });
  });

  describe("processEvent", () => {
    it("should enrich event with cpInfo and reActiveStatus", async () => {
      const mockEvent = { type: "click" };
      const mockCpInfo = { diversionMethod: "test-method" };
      getCurrentCpInfo.mockResolvedValue(mockCpInfo);
      determineReActiveStatus.mockReturnValue(1);

      const mockResponse = { data: { diversionMethod: "test-method" } };
      configApi.getCpConfig.mockResolvedValue(mockResponse);

      const result = await processEvent(mockEvent);
      expect(result).toEqual({
        ...mockEvent,
        diversionMethod: mockCpInfo.diversionMethod,
        reActive: 1,
      });
    });

    it("should return original event on error", async () => {
      const mockEvent = { type: "click" };
      getCurrentCpInfo.mockRejectedValue(new Error("test error"));

      const result = await processEvent(mockEvent);
      expect(result).toEqual(mockEvent);
    });
  });
});