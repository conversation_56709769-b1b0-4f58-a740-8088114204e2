/**
 * @vitest-environment jsdom
 */
import { describe, it, expect, beforeEach, vi, jest } from "vitest";
import httpServer from "../../src/helpers/http-server";

vi.mock("axios");
vi.mock("../../src/helpers/utils", () => ({
  generateRandom: vi.fn().mockReturnValue("123"),
  isProductionEnv: vi.fn(),
  sha256Hex: vi.fn().mockReturnValue("mock-signature"),
}));
vi.mock("../../src/helpers/logger/log-util");
vi.mock("../../src/helpers/mathUtils", () => ({
  isNumber: vi.fn().mockReturnValue(true),
}));

// Mock window.matchMedia for Node environment
Object.defineProperty(window, "matchMedia", {
  writable: true,
  value: vi.fn().mockImplementation((query) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

describe("http-server.js", () => {
  const mockContext = {
    userInfo: {
      accessToken: "mock-access-token",
      deviceId: "device-123",
      cid: "cid-123",
      "x-sub-cid": "sub-cid-123",
    },
    deviceInfo: {
      country: "CN",
      language: "zh",
      packageName: "com.hihonor.id",
      versionCode: "1.0.0",
      "x-oaid": "oaid-123",
    },
    agentInfo: {
      os: {
        name: "Android",
        version: "10",
      },
    },
    logContext: {
      traceId: "trace-123",
      deviceModel: "Honor 50",
      userId: "user-123",
      deviceId: "device-123",
    },
    mockFlag: "1",
  };

  const mockReq = {
    clientIp: "***********",
    headers: {
      host: "test.hihonorcloud.com",
    },
    originalUrl: "/wallet-loan-web/pages",
    cookies: {},
  };

  beforeEach(() => {
    global.asyncLocalStorage = {
      getStore: vi.fn().mockReturnValue({
        req: mockReq,
        res: {
          cookie: vi.fn(),
        },
        context: mockContext,
      }),
    };

    process.env.DEF_ENV_TYPE = "development";
    process.env.WALLET_ENV = "test";
  });

  describe("buildHeader", () => {
    it("should generate correct headers", () => {
      const config = {
        url: "/api/test",
        headers: {},
        data: {},
      };

      const headers = httpServer.buildHeader(mockReq, mockContext, config);

      expect(headers.nonce).toMatch(/^\d+123$/);
      expect(headers.timeStamp).toBeDefined();
      expect(headers["x-uuid"]).toBeDefined();
      expect(headers["access-token"]).toBe("mock-access-token");
      expect(headers["x-wallet-env"]).toBe("test");
      expect(headers["x-oaid"]).toBe("oaid-123");
      expect(headers.mockFlag).toBe("1");
      expect(headers["x-sign"]).toBe("mock-signature");
      expect(headers.traceId).toBe("trace-123");
      expect(headers.hnidVersion).toBe("1.0.0");
    });

    it("should handle x-wallet-key header", () => {
      const config = {
        url: "/api/test",
        headers: {
          "x-wallet-key": "wallet-key-123",
        },
        data: {},
      };

      const headers = httpServer.buildHeader(mockReq, mockContext, config);
      expect(headers["x-wallet-key"]).toBe("wallet-key-123");
    });
  });

  describe("request interceptor", () => {
    it("should modify config for withholdSignUrl", async () => {
      const config = {
        url: "/loan/api/user/withholdSignUrl",
        headers: {},
        data: {},
      };

      const modifiedConfig = await httpServer.interceptors.request.handlers[0].fulfilled(config);

      expect(modifiedConfig.data.returnUrl).toContain("wallet://business/loan");
      expect(modifiedConfig.data.returnUrl).toContain("canBack=true");
    });

    it("should use mock baseURL when mockFlag is set", async () => {
      mockContext.mockFlag = "123";
      const config = {
        url: "/api/test",
        headers: {},
        data: {},
      };

      const modifiedConfig = await httpServer.interceptors.request.handlers[0].fulfilled(config);
      expect(modifiedConfig.baseURL).toBe("https://card-dev-drcn.wallet.hihonorcloud.com/mock/123");
    });
  });

  describe("response interceptor", () => {
    it("should handle successful response", async () => {
      const response = {
        data: {
          code: 0,
          message: "success",
        },
        request: {
          path: "/api/test",
          getHeader: jest.fn().mockReturnValue("1234567890"),
        },
      };

      const result = await httpServer.interceptors.response.handlers[0].fulfilled(response);
      expect(result).toEqual(response);
    });

    it("should handle error response", async () => {
      const error = {
        message: "Request failed",
        response: {
          status: 500,
          data: {
            message: "Internal server error",
          },
        },
        request: {
          path: "/api/test",
        },
      };

      await expect(httpServer.interceptors.response.handlers[0].rejected(error)).rejects.toEqual(
        error,
      );
    });

    it("should set access_fail cookie for 1201 code", async () => {
      const response = {
        data: {
          code: 1201,
          message: "access denied",
        },
        request: {
          path: "/api/test",
          getHeader: jest.fn().mockReturnValue("1234567890"),
        },
      };

      const { res } = global.asyncLocalStorage.getStore();
      await httpServer.interceptors.response.handlers[0].fulfilled(response);
      expect(res.cookie).toHaveBeenCalledWith("access_fail", "1");
    });
  });

  describe("environment config", () => {
    it("should use correct baseURL for production", () => {
      process.env.DEF_ENV_TYPE = "production";
      expect(httpServer.defaults.baseURL).toBe("http://card-drcn.inner.wallet.hihonorcloud.com");
    });

    it("should use correct baseURL for development", () => {
      process.env.DEF_ENV_TYPE = "development";
      expect(httpServer.defaults.baseURL).toBe(
        "http://card-test-drcn.inner.wallet.hihonorcloud.com",
      );
    });
  });
});