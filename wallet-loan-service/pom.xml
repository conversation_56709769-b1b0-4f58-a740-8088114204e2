<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <artifactId>loan-server</artifactId>
        <groupId>com.hihonor.cloud.wallet</groupId>
        <version>1.0.0</version>
    </parent>

    <artifactId>wallet-loan-service</artifactId>

    <properties>
        <surefireArgLine>
            -Dspring.application.name=wallet-loan-service
        </surefireArgLine>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.hihonor.cloud.wallet</groupId>
            <artifactId>wallet-loan-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.alipay</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.41.559.DEV</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/libs/alipay-sdk-java-4.41.559.DEV.jar</systemPath>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.1.1</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>

        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>findbugs-maven-plugin</artifactId>
                <version>3.0.5</version>
                <configuration>
                    <findbugsXmlOutput>true</findbugsXmlOutput>
                    <effort>Max</effort>
                    <threshold>Low</threshold>
                    <timeout>9000000</timeout>
                    <maxHeap>1024</maxHeap>
                    <excludeFilterFile>${basedir}/src/main/resources/excludeFilter.xml</excludeFilterFile>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
            <plugin>
                <groupId>com.hihonor.generator</groupId>
                <artifactId>generator-maven-plugin</artifactId>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>