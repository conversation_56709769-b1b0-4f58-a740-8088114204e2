package com.hihonor.wallet.loan.client.ssd;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Map;
import java.util.UUID;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.AlipayObject;
import com.alipay.api.AlipayRequest;
import com.alipay.api.AlipayResponse;
import com.alipay.api.diagnosis.DiagnosisUtils;
import com.alipay.api.internal.util.AlipayEncrypt;
import com.alipay.api.internal.util.AlipaySignature;
import com.alipay.api.request.AlipayPcreditLoanHonorBudgetQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorLenddetailQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorPlanBatchqueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayApplyRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayBatchqueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayresultQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorUsercancelaccountApplyRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorUsercancelaccountCheckRequest;
import com.alipay.api.response.AlipayPcreditLoanHonorBudgetQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorLenddetailQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorPlanBatchqueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayApplyResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayBatchqueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayresultQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorUsercancelaccountApplyResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorUsercancelaccountCheckResponse;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.report.ReportLog;
import com.hihonor.wallet.common.util.JsonUtils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.common.util.log.SensitiveLog;
import com.hihonor.wallet.loan.client.config.LoanClientConfig;
import com.hihonor.wallet.loan.client.config.LoanClientConfigParam;
import com.hihonor.wallet.loan.client.general.constant.GeneralConstant;
import com.hihonor.wallet.loan.client.ssd.constant.SsdConstant;
import com.hihonor.wallet.loan.client.ssd.model.dto.SsdBaseDto;
import com.hihonor.wallet.loan.client.ssd.model.param.SsdBaseParam;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.enums.TrafficSourceEnum;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.json.JSONUtil;

/**
 * 随身贷客户端
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Component
public class SsdClient implements SsdApi{

    @Autowired
    private AlipayClient alipayClient;

    @Autowired
    private LoanClientConfig loanClientConfig;

    @Autowired
    private ReportLog reportLog;

    @Override
    public AlipayPcreditLoanHonorLenddetailQueryResponse queryLoanRecordDetail(
        AlipayPcreditLoanHonorLenddetailQueryRequest request) {
        return sendMessage(request, GeneralConstant.LOAN_RECORD_DETAIL_URI);
    }

    @Override
    public AlipayPcreditLoanHonorPlanBatchqueryResponse queryRepayPlan(
        AlipayPcreditLoanHonorPlanBatchqueryRequest request) {
        return sendMessage(request, GeneralConstant.REPAY_PLAN_URI);
    }

    @Override
    public AlipayPcreditLoanHonorBudgetQueryResponse repayCalculate(AlipayPcreditLoanHonorBudgetQueryRequest request) {
        return sendMessage(request, GeneralConstant.REPAY_CALCULATE_URI_GENERAL);
    }

    @Override
    public AlipayPcreditLoanHonorRepayApplyResponse repayDo(AlipayPcreditLoanHonorRepayApplyRequest request) {
        return sendMessage(request, GeneralConstant.REPAY_DO_URI_GENERAL);
    }

    @Override
    public AlipayPcreditLoanHonorRepayresultQueryResponse queryRepayResult(
        AlipayPcreditLoanHonorRepayresultQueryRequest request) {
        return sendMessage(request, GeneralConstant.REPAY_RESULT_URI_GENERAL);
    }

    @Override
    public AlipayPcreditLoanHonorRepayBatchqueryResponse queryRepayRecord(
        AlipayPcreditLoanHonorRepayBatchqueryRequest request) {
        return sendMessage(request, GeneralConstant.REPAY_RECORD_URI);
    }

    @Override
    public AlipayPcreditLoanHonorUsercancelaccountCheckResponse userCancelAccount(
        AlipayPcreditLoanHonorUsercancelaccountCheckRequest request) {
        return sendMessage(request, GeneralConstant.USER_CANCEL_CHECK);
    }

    @Override
    public AlipayPcreditLoanHonorUsercancelaccountApplyResponse userLogoff(
        AlipayPcreditLoanHonorUsercancelaccountApplyRequest request) {
        return sendMessage(request, GeneralConstant.USER_ACCOUNT_CANCEL);
    }

    private <T extends AlipayResponse> T sendMessage(AlipayRequest<T> request, String portName) {
        request.setNeedEncrypt(true);
        T response;

        LoanClientConfigParam loanClientConfigParam = loanClientConfig.getSpTsmConfig(LoanConstant.Supplier.SSD, null);

        String portNameWithPrefix = loanClientConfigParam.getName() + '_' + portName;
        if (!(portName.equals(GeneralConstant.LOAN_APPLY_URI)
            || StringUtils.equals(GeneralConstant.CREDIT_APPLY_RESULT_URI, portName)
            || StringUtils.equals(GeneralConstant.ID_CARD_CHECK, portName)
            || StringUtils.equals(GeneralConstant.LIMIT_APPLY_URI, portName))
            || StringUtils.equals(GeneralConstant.VERIFY_SMS_CODE_URI, portName)) {
            LogUtil.runSensitiveInfoLog(portNameWithPrefix + "接口参数： {}", SensitiveLog.hideMarkLog(JsonUtils.toJson(request)));
        } else {
            LogUtil.runInfoLog("调用{}接口:{}", loanClientConfigParam.getName(), portName);
        }

        long startTime = System.currentTimeMillis();
        try {
            // 测试联调时指定蚂蚁业务系统机器, 由于该参数位于子类，所以需要反射统一赋值
            String wsServiceUrl = loanClientConfigParam.getWsServiceUrl();
            if (ObjUtil.isNotEmpty(wsServiceUrl)) {
                Method method = request.getClass().getMethod(SsdConstant.COMMON_CHILD_METHOD_NAME, String.class, String.class);
                method.invoke(request, SsdConstant.COMMON_WS_URL_KEY, wsServiceUrl);
            }

            // 通过反射设置traceId
            AlipayObject model = request.getBizModel();
            Field traceIdField = model.getClass().getDeclaredField(SsdConstant.TRACE_ID_KEY);
            traceIdField.setAccessible(true);
            traceIdField.set(model, generateFlowNo());

            // 设置requestSource
            Field requestSourceField = model.getClass().getDeclaredField("requestSource");
            requestSourceField.setAccessible(true);
            Object currentValue = requestSourceField.get(model);
            if (currentValue == null) {
                requestSourceField.set(model, TrafficSourceEnum.CUSTOMER);
            }
            
            request.setNeedEncrypt(true);

            response = alipayClient.execute(request);
        }catch (Exception e) {
            LogUtil.runErrorLog("调用随身贷接口报错，错误原因：" + e.getMessage());
            throw new BusinessException(WalletResultCode.LOAN_SERVICE_ERROR);
        }

        if (response.isSuccess()) {
            LogUtil.runSensitiveInfoLog(portNameWithPrefix + "返回的接口参数解密后为： {}", SensitiveLog.hideMarkLog(JsonUtils.toJson(response)));
            reportLog.report(portNameWithPrefix, true, startTime);
            return response;
        } else {
            LogUtil.runErrorLog("调用随身贷接口报错，错误原因诊断链接：" + DiagnosisUtils.getDiagnosisUrl(response));
            throw new BusinessException(response.getSubCode(), response.getSubMsg());
        }
    }

    /**
     * 生成flowNo
     *
     * @return flowNo
     */
    private static String generateFlowNo() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString().replaceAll("-", "").substring(0, 32);
    }

    /**
     * 回调用的 解密方法
     *
     * @param params params
     * @return SsdBaseParam
     */
    @Override
    public SsdBaseParam getEncryptDataForNotify(Map<String, String> params) {
        SsdBaseParam paramDto = JsonUtils.parse(params, SsdBaseParam.class);
        LoanClientConfigParam loanClientConfigParam = loanClientConfig.getSpTsmConfig(LoanConstant.Supplier.SSD, null);

        boolean isCheckSignPass;
        LogUtil.runInfoLog("随身贷回调params:" + params);

        try {
            isCheckSignPass =
                AlipaySignature.rsaCheckV1(params, loanClientConfigParam.getSecretKey(), paramDto.getCharset(),
                    paramDto.getSign_type());
        } catch (Exception e) {
            LogUtil.runErrorLog("随身贷回调验签失败，错误原因：" + e.getMessage());
            throw new BusinessException(WalletResultCode.SP_VERIFY_SIGN_ERROR);
        }

        if (isCheckSignPass) {
            String bizContent = paramDto.getBiz_content();
            try {
                bizContent = AlipayEncrypt.decryptContent(bizContent, "AES", loanClientConfigParam.getAesKey(),
                    paramDto.getCharset());
            } catch (AlipayApiException e) {
                LogUtil.runErrorLog("随身贷回调解密失败，错误原因：" + e.getMessage());
                throw new BusinessException(WalletResultCode.SP_VERIFY_SIGN_ERROR);
            }
            paramDto.setBiz_content(bizContent);
        } else {
            LogUtil.runErrorLog("随身贷回调验签失败");
            throw new BusinessException(WalletResultCode.SP_VERIFY_SIGN_ERROR);
        }

        return paramDto;
    }

    /**
     * 签名
     *
     * @param dto dto
     * @return String
     */
    @Override
    public String sign(SsdBaseDto dto) {
        LoanClientConfigParam loanClientConfigParam = loanClientConfig.getSpTsmConfig(LoanConstant.Supplier.SSD, null);
        String sign;
        try {
            sign = AlipaySignature.rsaSign(JSONUtil.toJsonStr(dto), loanClientConfigParam.getPrivateKey(), "UTF-8",
                "RSA2");
        } catch (Exception e) {
            LogUtil.runErrorLog("随身贷回调签名失败，错误原因：" + e.getMessage());
            throw new BusinessException(WalletResultCode.LOAN_SERVICE_ERROR);
        }
        return sign;
    }
}
