package com.hihonor.wallet.loan.client.ssd.converter.repay;

import java.util.List;

import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import com.alipay.api.domain.HonorContractDTO;
import com.alipay.api.domain.HonorRepayPlanDTO;
import com.alipay.api.domain.HonorRepayPlanTermDTO;
import com.alipay.api.domain.HonorRepayRecordDTO;
import com.alipay.api.response.AlipayPcreditLoanHonorBudgetQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorLenddetailQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayresultQueryResponse;
import com.hihonor.wallet.loan.client.model.dto.QueryRepayResultDto;
import com.hihonor.wallet.loan.model.dto.ContractDto;
import com.hihonor.wallet.loan.model.dto.LoanRecordDetailDto;
import com.hihonor.wallet.loan.client.model.dto.RepayCalculateDto;
import com.hihonor.wallet.loan.client.ssd.converter.common.SsdAmountCommonConverter;
import com.hihonor.wallet.loan.model.dto.RepayDto;
import com.hihonor.wallet.loan.model.dto.RepayPlanDto;
import com.hihonor.wallet.loan.model.dto.RepayPlanTermDto;
import com.hihonor.wallet.loan.model.dto.RepayType;

import cn.hutool.core.date.DateUtil;

/**
 * 还款计划类型转换器
 *
 * <AUTHOR>
 * @since 2025-08-15
 */
@Mapper(componentModel = "spring", uses = {SsdAmountCommonConverter.class})
public interface SsdRepayConverter {
    LoanRecordDetailDto convertLoanRecordDetail(AlipayPcreditLoanHonorLenddetailQueryResponse source);

    ContractDto convertContract(HonorContractDTO source);

    List<RepayPlanDto> convertRepayPlanList(List<HonorRepayPlanDTO> source);

    @Mapping(source = "overdueDays", target = "loanOverDueDays")
    @Mapping(source = "overdueAmount", target = "loanOverDueAmount")
    @Mapping(target = "currentDueDate", expression = "java(this.calculateCurrentDueDate(source))")
    RepayPlanDto convertRepayPlan(HonorRepayPlanDTO source);

    @Mapping(source = "overdueDays", target = "loanOverDueDays")
    RepayPlanTermDto convertRepayPlanTerm(HonorRepayPlanTermDTO source);

    RepayCalculateDto convertTrialResponse(AlipayPcreditLoanHonorBudgetQueryResponse source);

    QueryRepayResultDto convertRepayResult(AlipayPcreditLoanHonorRepayresultQueryResponse response);

    List<RepayDto> convertRepayRecords(List<HonorRepayRecordDTO> records);

    RepayDto convertRepayRecord(HonorRepayRecordDTO source);

    default RepayType toRepayType(Integer typeValue) {
        if (typeValue == null) {
            return null;
        }
        RepayType repayType = new RepayType();
        repayType.setType(typeValue);
        repayType.setDisabled(false);
        return repayType;
    }

    default Boolean calculateCurrentDueDate(HonorRepayPlanDTO dto) {
        if (dto == null || dto.getRepayPlanTerms() == null) {
            return false;
        }

        String todayDate = DateUtil.format(DateUtil.date(), "yyyyMMdd");
        return dto.getRepayPlanTerms()
            .stream()
            .anyMatch(repayPlanTermDto -> todayDate.equals(repayPlanTermDto.getShouldRepayDate()));
    }
}
