/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hihonor.wallet.common.exception.BusinessException;
import com.hihonor.wallet.common.exception.WalletResultCode;
import com.hihonor.wallet.common.redis.RedisLock;
import com.hihonor.wallet.common.util.EncryptUtil;
import com.hihonor.wallet.common.util.IdUtils;
import com.hihonor.wallet.common.util.JsonUtils;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.common.util.SHA256Utils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.common.util.log.SensitiveLog;
import com.hihonor.wallet.loan.client.LoanClient;
import com.hihonor.wallet.loan.client.general.constant.GeneralConstant;
import com.hihonor.wallet.loan.client.model.dto.*;
import com.hihonor.wallet.loan.client.model.param.*;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.*;
import com.hihonor.wallet.loan.mapper.*;
import com.hihonor.wallet.loan.model.dto.*;
import com.hihonor.wallet.loan.model.param.QueryRepayResultParam;
import com.hihonor.wallet.loan.model.param.*;
import com.hihonor.wallet.loan.service.LoanSupplierService;
import com.hihonor.wallet.loan.service.RepayService;
import com.hihonor.wallet.loan.service.SmsService;
import com.hihonor.wallet.loan.service.UserService;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import javax.validation.ValidationException;

import cn.hutool.core.util.ObjUtil;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-01
 */
@Service
@RefreshScope
public class RepayServiceImpl extends ServiceImpl<LoanRepayMapper, LoanRepayEntity>
        implements RepayService {
    /**
     * 还款短信类型
     */
    private static final Integer REPAY_SMS_TYPE = 1;

    @Autowired
    private LoanClient loanClient;

    @Autowired
    private UserService userService;

    @Autowired
    private LoanRepayResultMapper loanRepayResultMapper;

    @Autowired
    private LoanRepayTermMapper loanRepayTermMapper;

    @Autowired
    private LoanRepayTermRecordMapper loanRepayTermRecordMapper;

    @Autowired
    private LoanApplyMapper loanApplyMapper;

    @Autowired
    private LoanRepayRecordMapper loanRepayRecordMapper;

    @Autowired
    private SmsService smsService;

    @Autowired
    private CreditApplyMapper creditApplyMapper;

    @Autowired
    private LoanSupplierService loanSupplierService;

    @Autowired
    private LoanRepayMapper loanRepayMapper;

    @Value("${repay.transfer.backUrl}")
    private String transferBackUrl;

    @Override
    public RepayTrialResponse repayTrial(RepayTrialParam param, String redisLockPostName) {
        LogUtil.runInfoLog("还款试算 入参{}", SensitiveLog.hideMarkLog(JsonUtils.toJson(param)));
        LoanUserEntity loanUserEntity = userService.getUserAccessInfo();
        String outOrderNo = param.getOutOrderNo();
        LambdaQueryWrapper<LoanApplyEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LoanApplyEntity::getOutOrderNo, outOrderNo)
                .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId());
        LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(lambdaQueryWrapper);
        if (loanApplyEntity == null) {
            throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "未根据订单号找到对应的借款申请记录");
        }
        RepayCalculateParam repayCalculateParam = new RepayCalculateParam();

        BeanUtils.copyProperties(param, repayCalculateParam);
        repayCalculateParam.setBaseRequest(param);
        repayCalculateParam.setUserId(loanUserEntity.getOpenId());
        repayCalculateParam.setOpenId(loanUserEntity.getOutOpenId());
        repayCalculateParam.setSupplier(loanApplyEntity.getSupplier());
        repayCalculateParam.setOrderId(loanApplyEntity.getApplyNo());
        RepayCalculateDto dto = loanClient.repayCalculate(repayCalculateParam);
        RepayTrialResponse repayTrialResponse = new RepayTrialResponse();
        BeanUtils.copyProperties(dto, repayTrialResponse);
        repayTrialResponse.setUsedCoupons(dto.getCoupon());
        repayTrialResponse.setUsableCoupons(dto.getUsableRepayCoupons());
        repayTrialResponse.setRepayPlanTerms(dto.getRepayPlanTerms());
        String nowDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        repayTrialResponse.setNowDate(nowDate);

        return repayTrialResponse;
    }

    @Override
    @RedisLock(name = "repayCheck", time = 1)
    public RepayCheckResponse repayCheck(RepayCheckParam param, String redisLockPostName) {
        LogUtil.runInfoLog("repayCheck 入参{}", SensitiveLog.hideMarkLog(JsonUtils.toJson(param)));
        LoanUserEntity loanUserEntity = userService.getUserAccessInfo();
        String outOrderNo = param.getOutOrderNo();
        LambdaQueryWrapper<LoanApplyEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        lambdaQueryWrapper.eq(LoanApplyEntity::getOutOrderNo, outOrderNo)
                .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId());
        LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(lambdaQueryWrapper);
        if (loanApplyEntity == null) {
            throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "未根据订单号找到对应的借款申请记录");
        }
        ResignCheckParam resignCheckParam = new ResignCheckParam();
        resignCheckParam.setOrderId(param.getOutOrderNo());
        resignCheckParam.setBankCardId(param.getBankCardId());
        resignCheckParam.setTotalAmount(param.getTotalAmount());
        resignCheckParam.setOpenId(loanUserEntity.getOutOpenId());
        resignCheckParam.setUserId(loanUserEntity.getOpenId());
        resignCheckParam.setSupplier(Integer.parseInt(RequestUtils.getSupplier()));

        // 根据cp是否支持重新签约查询做不同处理
        Boolean isSupportResignCheck =
                loanSupplierService.getCpConfigBySupplierId(Integer.parseInt(RequestUtils.getSupplier()))
                        .checkIsSupportResign();
        ResignCheckDto resignCheckDto = new ResignCheckDto();
        if (isSupportResignCheck) {
            resignCheckDto = loanClient.resignCheck(resignCheckParam);
        } else {
            resignCheckDto.setNeedResign(0);
        }

        RepayCheckResponse repayCheckDto = new RepayCheckResponse();
        if (resignCheckDto.getNeedResign() == 1) {
            repayCheckDto.setStatus(0);
            repayCheckDto.setNeedResign(true);
            return repayCheckDto;
        }
        String serialNo = sendSmsCode(loanUserEntity, repayCheckDto);
        if (Objects.nonNull(repayCheckDto.getStatus()) && repayCheckDto.getStatus().equals(GeneralConstant.VerificationCodeSendingResult.FAIL)) {
            return repayCheckDto;
        }
        repayCheckDto.setStatus(GeneralConstant.VerificationCodeSendingResult.SUCCESS);
        repayCheckDto.setNeedResign(false);
        repayCheckDto.setSerialNo(serialNo);
        return repayCheckDto;
    }

    @Override
    @RedisLock(name = "repayDo", time = 1)
    public RepayResponse repayDo(RepayParam param, String redisLockPostName) {
        LogUtil.runInfoLog("主动还款 入参 {}", SensitiveLog.hideMarkLog(JsonUtils.toJson(param)));
        LoanUserEntity loanUserEntity = userService.getUserAccessInfo();
        String repayNo = IdUtils.generateRepayOrderId(RequestUtils.getUid(), Integer.parseInt(RequestUtils.getSupplier()));
        checkResign(param, loanUserEntity);
        RepayDoDto dto = doRepay(param, loanUserEntity, repayNo);
        LoanRepayEntity loanRepayEntity = doAfterRepay(param, loanUserEntity, repayNo, dto);
        RepayResponse repayResponse = new RepayResponse();
        repayResponse.setRepayNo(loanRepayEntity.getRepayNo());
        repayResponse.setOrderInfo(dto.getOrderInfo());
        return repayResponse;
    }


    @Override
    public TransferRepayDto transferRepay(TransferRepayParam param) {
        LogUtil.runInfoLog("转账还款 入参 {}", SensitiveLog.hideMarkLog(JsonUtils.toJson(param)));
        LoanUserEntity loanUserEntity = userService.getUserAccessInfo();
        String repayNo = IdUtils.generateRepayOrderId(RequestUtils.getUid(), Integer.parseInt(RequestUtils.getSupplier()));

        TransferRepayDoParam transferRepayDoParam = TransferRepayDoParam.from(loanUserEntity, repayNo, param, transferBackUrl);

        TransferRepayDoDto transferRepayDoResponse = loanClient.transferRepay(transferRepayDoParam);

        LoanRepayEntity loanRepayEntity = doAfterTransferRepay(transferRepayDoResponse, param, loanUserEntity, repayNo);
        return TransferRepayDto.builder()
                .repayNo(loanRepayEntity.getRepayNo())
                .repayUrl(transferRepayDoResponse.getRedirectUrl())
                .build();
    }

    @Override
    @RedisLock(name = "queryRepayResult", time = 1)
    public QueryRepayResultResponse queryRepayResult(com.hihonor.wallet.loan.model.param.QueryRepayResultParam param, String redisLockPostName) {
        LogUtil.runInfoLog("还款结果查询 入参 {}", SensitiveLog.hideMarkLog(JsonUtils.toJson(param)));
        LoanUserEntity loanUserEntity = userService.getUserAccessInfo();
        com.hihonor.wallet.loan.client.model.param.QueryRepayResultParam queryRepayResultParam = new com.hihonor.wallet.loan.client.model.param.QueryRepayResultParam();
        queryRepayResultParam.setTransNo(param.getRepayNo());
        queryRepayResultParam.setOpenId(loanUserEntity.getOutOpenId());
        queryRepayResultParam.setUserId(loanUserEntity.getOpenId());
        queryRepayResultParam.setSupplier(Integer.parseInt(RequestUtils.getSupplier()));
        com.hihonor.wallet.loan.client.model.dto.QueryRepayResultDto queryRepayResultDto
                = loanClient.queryRepayResult(queryRepayResultParam);
        QueryRepayResultResponse queryRepayResultResponse = new QueryRepayResultResponse();
        queryRepayResultResponse.setRepayStatus(queryRepayResultDto.getRepayStatus());
        queryRepayResultResponse.setRepayResult(queryRepayResultDto.getRepayResult());
        if (Objects.equals(LoanConstant.RepayStatus.REPAY_SUCCESS, queryRepayResultDto.getRepayStatus())
                || Objects.equals(LoanConstant.RepayStatus.REPAY_PART_SUCCESS, queryRepayResultDto.getRepayStatus())) {
            userService.deleteEntryInfoCache(loanUserEntity.getUserId().toString());
            Integer supportReoffer = loanSupplierService.getCpConfigBySupplierId(Integer.parseInt(RequestUtils.getSupplier())).getSupportReoffer();
            queryRepayResultResponse.setSupprtReoffer(supportReoffer);
        }
        return queryRepayResultResponse;
    }

    @Override
    public List<RepayPlanDto> queryRepayPlan(RepayPlanParam param) {
        LoanUserEntity loanUserEntity = userService.getUserAccessInfoV2();
        if (loanUserEntity == null) {
            return new ArrayList<>();
        }
        List<CreditApplyEntity> creditApplyEntities = creditApplyMapper.selectList(new LambdaQueryWrapper<CreditApplyEntity>()
                .eq(CreditApplyEntity::getUserId, loanUserEntity.getUserId())
                .eq(CreditApplyEntity::getSupplier, loanUserEntity.getSupplier())
                .eq(CreditApplyEntity::getApplyStatus, LoanConstant.CreditStatus.APPROVED)
                .orderByDesc(CreditApplyEntity::getCreateTime).last("LIMIT 1"));
        if (CollectionUtils.isEmpty(creditApplyEntities)) {
            return new ArrayList<>();
        }
        QueryRepayPlanParam queryRepayPlanParam = new QueryRepayPlanParam();
        queryRepayPlanParam.setSupplier(loanUserEntity.getSupplier());
        queryRepayPlanParam.setUserId(loanUserEntity.getOpenId());
        queryRepayPlanParam.setOpenId(loanUserEntity.getOutOpenId());
        if (param.getOutOrderNo() != null && loanUserEntity.getSupplier() != null && !loanUserEntity.getSupplier().equals(LoanConstant.Supplier.DXM)) {
            LoanApplyEntity entity = loanApplyMapper.selectOne(new LambdaQueryWrapper<LoanApplyEntity>().eq(LoanApplyEntity::getOutOrderNo, param.getOutOrderNo()));
            if (entity != null) queryRepayPlanParam.setApplyNo(entity.getApplyNo());
        }
        if (StringUtils.isNotBlank(param.getOutOrderNo())) {
            queryRepayPlanParam.setOutOrderNoList(Collections.singletonList(param.getOutOrderNo()));
        }
        List<RepayPlanDto> repayPlanDtos = loanClient.queryRepayPlan(queryRepayPlanParam);
        repayPlanDtos.forEach(repayPlanDto -> {
            LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(new LambdaQueryWrapper<LoanApplyEntity>().eq(LoanApplyEntity::getOutOrderNo, repayPlanDto.getOutOrderNo()));
            if (loanApplyEntity != null) {
                repayPlanDto.setApplyNo(loanApplyEntity.getApplyNo());
            }
        });

        // 还款日越早的排在前面
        repayPlanDtos.sort(Comparator.comparing(repayPlanDto -> {
            if (repayPlanDto.getRepayPlanTerms() != null && !repayPlanDto.getRepayPlanTerms().isEmpty()) {
                return repayPlanDto.getRepayPlanTerms().get(0).getShouldRepayDate();
            }
            return ""; // 如果没有还款计划，返回空字符串作为默认值
        }));
        return repayPlanDtos;
    }

    @Override
    public List<RepayDto> repayRecord(RepayRecordParam param) {
        String outOrderNo;
        Long userId = Long.parseLong(RequestUtils.getUid());
        if (ObjUtil.isNotEmpty(param.getOutOrderNo())) {
            outOrderNo = param.getOutOrderNo();
        } else if (ObjUtil.isNotEmpty(param.getRepayNo())) {
            LoanRepayEntity loanRepayEntity = loanRepayMapper.selectOne(new LambdaQueryWrapper<LoanRepayEntity>()
                    .eq(LoanRepayEntity::getRepayNo, param.getRepayNo())
                    .eq(LoanRepayEntity::getUserId, userId)
            );
            if (loanRepayEntity == null) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM, "该还款记录不存在");
            }
            outOrderNo = loanRepayEntity.getOutLoanNo();
        } else {
            throw new ValidationException("outOrderNo和repayNo不能同时为空");
        }


        LoanUserEntity loanUserEntity = userService.getUserAccessInfo();
        QueryRepayRecordParam queryRepayRecordParam = new QueryRepayRecordParam();
        queryRepayRecordParam.setLoanOrderId(outOrderNo);
        queryRepayRecordParam.setSupplier(loanUserEntity.getSupplier());
        queryRepayRecordParam.setUserId(loanUserEntity.getOpenId());
        queryRepayRecordParam.setOpenId(loanUserEntity.getOutOpenId());
        return loanClient.queryRepayRecord(queryRepayRecordParam);
    }

    /**
     * 查询后的数据库操作处理
     *
     * @param queryRepayResultDto queryRepayResultDto
     */
    private void doAfterQueryRepayStatus(com.hihonor.wallet.loan.client.model.dto.QueryRepayResultDto queryRepayResultDto,
                                         QueryRepayResultParam param, LoanUserEntity loanUserEntity) {
        Long userId = Long.parseLong(RequestUtils.getUid());
        LambdaUpdateWrapper<LoanRepayEntity> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(LoanRepayEntity::getRepayNo, param.getRepayNo());
        lambdaUpdateWrapper.eq(LoanRepayEntity::getUserId, userId);
        lambdaUpdateWrapper.set(LoanRepayEntity::getRepayStatus, queryRepayResultDto.getRepayStatus());
        lambdaUpdateWrapper.set(LoanRepayEntity::getRepayResult, queryRepayResultDto.getRepayResult());
        LoanRepayEncryptInfo loanRepayEncryptInfo = new LoanRepayEncryptInfo();
        loanRepayEncryptInfo.setCouponAmount(queryRepayResultDto.getCouponAmount());
        loanRepayEncryptInfo.setRepayAmount(queryRepayResultDto.getRepayAmount());
        lambdaUpdateWrapper.set(LoanRepayEntity::getAmountInfo, EncryptUtil.encrypt(JsonUtils.toJson(loanRepayEncryptInfo)));
        this.update(lambdaUpdateWrapper);
        List<RepayResultList> repayResultList = queryRepayResultDto.getRepayResultList();

        for (RepayResultList result : repayResultList) {
            if (Objects.equals(LoanConstant.RepayStatus.REPAY_SUCCESS, result.getStatus())
                    || Objects.equals(LoanConstant.RepayStatus.REPAY_PART_SUCCESS, result.getStatus())) {
                LoanRepayResultEntity loanRepayResultEntity = new LoanRepayResultEntity();
                loanRepayResultEntity.setRepayStatus(result.getStatus());
                loanRepayResultEntity.setUserId(userId);
                loanRepayResultEntity.setRepayNo(param.getRepayNo());
                loanRepayResultEntity.setOutOpenId(loanUserEntity.getOutOpenId());
                loanRepayResultEntity.setSupplier(loanUserEntity.getSupplier());
                loanRepayResultEntity.setOutOrderNo(result.getOutOrderNo());
                LambdaQueryWrapper<LoanApplyEntity> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(LoanApplyEntity::getOutOrderNo, result.getOutOrderNo())
                        .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId());
                LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(lambdaQueryWrapper);
                loanRepayResultEntity.setCid(loanApplyEntity.getCid());
                loanRepayResultEntity.setLoc(loanApplyEntity.getLoc());
                loanRepayResultEntity.setSubCid(loanApplyEntity.getSubCid());
                LoanRepayResultEncryptInfo info = new LoanRepayResultEncryptInfo();
                info.setRepayAmount(result.getAmount());
                info.setReductionAmount(result.getReductionAmount());
                loanRepayResultEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(info)));
                loanRepayResultMapper.insert(loanRepayResultEntity);
                List<RepayItems> repayItems = result.getRepayItems();
                updateTermInfo(repayItems, result, param, loanUserEntity, loanApplyEntity);
            }
        }
    }

    /**
     * 期次还款记录处理
     *
     * @param repayItems      repayItems
     * @param repayResult     repayResult
     * @param param           param
     * @param loanUserEntity  loanUserEntity
     * @param loanApplyEntity loanApplyEntity
     */
    private void updateTermInfo(List<RepayItems> repayItems, RepayResultList repayResult, QueryRepayResultParam param,
                                LoanUserEntity loanUserEntity, LoanApplyEntity loanApplyEntity) {
        if (CollectionUtils.isEmpty(repayItems) || (LoanConstant.RepayStatus.REPAY_SUCCESS != repayResult.getStatus()
                && LoanConstant.RepayStatus.REPAY_PART_SUCCESS != repayResult.getStatus())) {
            return;
        }
        for (RepayItems item : repayItems) {
            LambdaUpdateWrapper<LoanRepayTermEntity> resultWrapper = new LambdaUpdateWrapper<>();
            resultWrapper.eq(LoanRepayTermEntity::getRepayNo, param.getRepayNo())
                    .eq(LoanRepayTermEntity::getOutOrderNo, repayResult.getOutOrderNo())
                    .eq(LoanRepayTermEntity::getTermNo, item.getTermNo());
            LoanRepayTermEntity loanRepayTermEntity = new LoanRepayTermEntity();
            boolean exist = loanRepayTermMapper.exists(resultWrapper);
            if (exist) {
                return;
            }
            loanRepayTermEntity.setRepayNo(param.getRepayNo());
            loanRepayTermEntity.setTermNo(item.getTermNo());
            loanRepayTermEntity.setUserId(loanUserEntity.getUserId());
            loanRepayTermEntity.setOutOpenId(loanUserEntity.getOutOpenId());
            loanRepayTermEntity.setSupplier(loanUserEntity.getSupplier());
            loanRepayTermEntity.setCid(loanApplyEntity.getCid());
            loanRepayTermEntity.setSubCid(loanApplyEntity.getSubCid());
            loanRepayTermEntity.setLoc(loanApplyEntity.getLoc());
            loanRepayTermEntity.setOutOrderNo(repayResult.getOutOrderNo());
            LoanRepayTermEncryptInfo loanRepayTermEncryptInfo = new LoanRepayTermEncryptInfo();
            loanRepayTermEncryptInfo.setTermAmount(item.getTermAmount());
            loanRepayTermEncryptInfo.setTermPrincipal(item.getTermPrincipal());
            loanRepayTermEncryptInfo.setTermInterest(item.getTermInterest());
            loanRepayTermEncryptInfo.setTermPrinPenalty(item.getTermPrinPenalty());
            loanRepayTermEncryptInfo.setTermInterPenalty(item.getTermInterPenalty());
            if (item.getTermDiscount() != null) {
                loanRepayTermEncryptInfo.setTermDiscount(item.getTermDiscount());
            }
            if (item.getTermViolateFee() != null) {
                loanRepayTermEncryptInfo.setTermViolateFee(item.getTermViolateFee());
            }
            loanRepayTermEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(loanRepayTermEncryptInfo)));
            loanRepayTermMapper.insert(loanRepayTermEntity);
            LoanRepayTermRecordEntity loanRepayTermRecordEntity = new LoanRepayTermRecordEntity();
            BeanUtils.copyProperties(loanRepayTermEntity, loanRepayTermRecordEntity);
            BeanUtils.copyProperties(loanRepayTermEncryptInfo, loanRepayTermRecordEntity);
            loanRepayTermRecordMapper.insert(loanRepayTermRecordEntity);
        }
    }

    /**
     * 主动退款后的操作
     *
     * @param param          param
     * @param loanUserEntity loanUserEntity
     */
    private LoanRepayEntity doAfterRepay(RepayParam param, LoanUserEntity loanUserEntity, String repayNo, RepayDoDto dto) {
        Long userId = Long.parseLong(RequestUtils.getUid());
        Integer supplier = Integer.parseInt(RequestUtils.getSupplier());
        Long couponAmount = param.getRepayItemList().stream()
                .filter(item -> item.getReductionAmount() != null)
                .mapToLong(RepayItemParam::getReductionAmount)
                .sum();
        LoanRepayEntity loanRepayEntity = new LoanRepayEntity();
        loanRepayEntity.setRepayNo(repayNo);
        loanRepayEntity.setUserId(userId);
        loanRepayEntity.setSupplier(supplier);
        loanRepayEntity.setOutOpenId(loanUserEntity.getOutOpenId());
        loanRepayEntity.setOutLoanNo(param.getRepayItemList().get(0).getOutOrderNo());
        loanRepayEntity.setOutOrderNo(dto.getRepayOrderId());
        loanRepayEntity.setRepayStatus(LoanConstant.RepayStatus.REPAYING);
        loanRepayEntity.setRepayPart(param.getRepayPart() ? 1 : 2);
        loanRepayEntity.setRepayType(param.getRepayType());
        loanRepayEntity.setDeviceId(RequestUtils.getBaseHeader().getDeviceId());
        loanRepayEntity.setDeviceModel(RequestUtils.getBaseHeader().getDeviceModel());
        LoanRepayEncryptInfo loanRepayEncryptInfo = new LoanRepayEncryptInfo();
        loanRepayEncryptInfo.setCouponAmount(couponAmount);
        loanRepayEncryptInfo.setRepayAmount(param.getTotalAmount());
        loanRepayEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(loanRepayEncryptInfo)));
        this.save(loanRepayEntity);
        LoanRepayRecordEntity loanRepayRecordEntity = new LoanRepayRecordEntity();
        BeanUtils.copyProperties(loanRepayEntity, loanRepayRecordEntity);

        loanRepayRecordMapper.insert(loanRepayRecordEntity);
        generateRepayResult(param, loanUserEntity, repayNo);

        return loanRepayEntity;
    }

    /**
     * 转账还款后的操作
     *
     * @param dto            三方返回
     * @param param          入参
     * @param loanUserEntity 用户信息
     * @param repayNo        还款单号
     * @return 还款记录
     */
    private LoanRepayEntity doAfterTransferRepay(TransferRepayDoDto dto, TransferRepayParam param, LoanUserEntity loanUserEntity, String repayNo) {
        Long userId = Long.parseLong(RequestUtils.getUid());
        Integer supplier = Integer.parseInt(RequestUtils.getSupplier());
        Long couponAmount = param.getRepayItemList().stream()
                .filter(item -> item.getReductionAmount() != null)
                .mapToLong(RepayItemParam::getReductionAmount)
                .sum();
        LoanRepayEntity loanRepayEntity = new LoanRepayEntity();
        loanRepayEntity.setRepayNo(repayNo);
        loanRepayEntity.setUserId(userId);
        loanRepayEntity.setSupplier(supplier);
        loanRepayEntity.setOutOpenId(loanUserEntity.getOutOpenId());
        loanRepayEntity.setOutLoanNo(param.getRepayItemList().get(0).getOutOrderNo());
        loanRepayEntity.setOutOrderNo(dto.getRepayOrderId());
        loanRepayEntity.setRepayStatus(LoanConstant.RepayStatus.REPAYING);
        loanRepayEntity.setRepayType(param.getRepayType());
        loanRepayEntity.setDeviceId(RequestUtils.getBaseHeader().getDeviceId());
        loanRepayEntity.setDeviceModel(RequestUtils.getBaseHeader().getDeviceModel());
        LoanRepayEncryptInfo loanRepayEncryptInfo = new LoanRepayEncryptInfo();
        loanRepayEncryptInfo.setCouponAmount(couponAmount);
        loanRepayEncryptInfo.setRepayAmount(param.getTotalAmount());
        loanRepayEntity.setAmountInfo(EncryptUtil.encrypt(JsonUtils.toJson(loanRepayEncryptInfo)));
        this.save(loanRepayEntity);
        LoanRepayRecordEntity loanRepayRecordEntity = new LoanRepayRecordEntity();
        BeanUtils.copyProperties(loanRepayEntity, loanRepayRecordEntity);

        loanRepayRecordMapper.insert(loanRepayRecordEntity);

        generateTransferRepayResult(param, loanUserEntity, repayNo);
        return loanRepayEntity;
    }

    /**
     * result表创建
     *
     * @param param          param
     * @param loanUserEntity loanUserEntity
     * @param repayNo        repayNo
     */
    private void generateRepayResult(RepayParam param, LoanUserEntity loanUserEntity, String repayNo) {
        List<RepayItemParam> repayItemList = param.getRepayItemList();
        for (RepayItemParam repayItemParam : repayItemList) {
            LambdaQueryWrapper<LoanApplyEntity> loanApplyWrapper = new LambdaQueryWrapper<>();
            String outOrderNo = repayItemParam.getOutOrderNo();
            loanApplyWrapper.eq(LoanApplyEntity::getOutOrderNo, outOrderNo)
                    .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId()).or().eq(LoanApplyEntity::getApplyNo, outOrderNo)
                    .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId());
            LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(loanApplyWrapper);
            LoanRepayResultEntity loanRepayResultEntity = new LoanRepayResultEntity();
            loanRepayResultEntity.setRepayStatus(LoanConstant.RepayStatus.REPAYING);
            loanRepayResultEntity.setUserId(loanUserEntity.getUserId());
            loanRepayResultEntity.setRepayNo(repayNo);
            loanRepayResultEntity.setOutOpenId(loanUserEntity.getOutOpenId());
            loanRepayResultEntity.setSupplier(loanUserEntity.getSupplier());
            loanRepayResultEntity.setOutOrderNo(outOrderNo);
            loanRepayResultEntity.setCouponNo(repayItemParam.getCouponNo());
            loanRepayResultEntity.setCid(loanApplyEntity.getCid());
            loanRepayResultEntity.setLoc(loanApplyEntity.getLoc());
            loanRepayResultEntity.setSubCid(loanApplyEntity.getSubCid());
            loanRepayResultEntity.setDeviceId(loanApplyEntity.getDeviceId());
            loanRepayResultEntity.setDeviceModel(loanApplyEntity.getDeviceModel());
            loanRepayResultMapper.insert(loanRepayResultEntity);
        }

    }

    private void generateTransferRepayResult(TransferRepayParam param, LoanUserEntity loanUserEntity, String repayNo) {
        List<RepayItemParam> repayItemList = param.getRepayItemList();
        for (RepayItemParam repayItemParam : repayItemList) {
            LambdaQueryWrapper<LoanApplyEntity> loanApplyWrapper = new LambdaQueryWrapper<>();
            String outOrderNo = repayItemParam.getOutOrderNo();
            loanApplyWrapper.eq(LoanApplyEntity::getOutOrderNo, outOrderNo)
                    .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId()).or().eq(LoanApplyEntity::getApplyNo, outOrderNo)
                    .eq(LoanApplyEntity::getUserId, loanUserEntity.getUserId());
            LoanApplyEntity loanApplyEntity = loanApplyMapper.selectOne(loanApplyWrapper);
            LoanRepayResultEntity loanRepayResultEntity = new LoanRepayResultEntity();
            loanRepayResultEntity.setRepayStatus(LoanConstant.RepayStatus.REPAYING);
            loanRepayResultEntity.setUserId(loanUserEntity.getUserId());
            loanRepayResultEntity.setRepayNo(repayNo);
            loanRepayResultEntity.setOutOpenId(loanUserEntity.getOutOpenId());
            loanRepayResultEntity.setSupplier(loanUserEntity.getSupplier());
            loanRepayResultEntity.setOutOrderNo(outOrderNo);
            loanRepayResultEntity.setCid(loanApplyEntity.getCid());
            loanRepayResultEntity.setLoc(loanApplyEntity.getLoc());
            loanRepayResultEntity.setSubCid(loanApplyEntity.getSubCid());
            loanRepayResultEntity.setDeviceId(loanApplyEntity.getDeviceId());
            loanRepayResultEntity.setDeviceModel(loanApplyEntity.getDeviceModel());
            loanRepayResultMapper.insert(loanRepayResultEntity);
        }

    }


    /**
     * 主动还款
     *
     * @param param          param
     * @param loanUserEntity loanUserEntity
     */
    private RepayDoDto doRepay(RepayParam param, LoanUserEntity loanUserEntity, String transNo) {
        RepayDoParam repayDoParam = new RepayDoParam();
        BeanUtils.copyProperties(param, repayDoParam);
        repayDoParam.setRepayItemList(param.getRepayItemList());
        repayDoParam.setTransNo(transNo);
        repayDoParam.setOpenId(loanUserEntity.getOutOpenId());
        repayDoParam.setUserId(loanUserEntity.getOpenId());
        repayDoParam.setSupplier(Integer.parseInt(RequestUtils.getSupplier()));
        repayDoParam.setRepayTerms(param.getRepayItemList().get(0).getRepayTerms());
        if (!loanUserEntity.getSupplier().equals(LoanConstant.Supplier.PPD)) {
            repayDoParam.setRepayPart(null);
        }
        return loanClient.repayDo(repayDoParam);
    }

    /**
     * 检查是否需要重新签约
     *
     * @param param          param
     * @param loanUserEntity loanUserEntity
     */
    private void checkResign(RepayParam param, LoanUserEntity loanUserEntity) {
        Integer supplier = Integer.parseInt(RequestUtils.getSupplier());
        if (param.getNeedResign()) {
            VerifyBindSmsParam verifyBindSmsParam = new VerifyBindSmsParam();
            verifyBindSmsParam.setReVerifyCode(param.getSmsCode());
            verifyBindSmsParam.setOpenId(loanUserEntity.getOutOpenId());
            verifyBindSmsParam.setUserId(loanUserEntity.getOpenId());
            verifyBindSmsParam.setBankCardId(param.getBankCardId());
            verifyBindSmsParam.setSupplier(supplier);
            VerifyBindSmsDto verifyBindSmsDto = loanClient.verifyBindSms(verifyBindSmsParam);
            if (!verifyBindSmsDto.getResult()) {
                throw new BusinessException(WalletResultCode.VERIFY_SMSCODE_ERROR);
            }
        } else {
            LoanSupplierDto cpConfig = loanSupplierService.getCpConfig();
            if (!Objects.isNull(cpConfig.getNeedRepaySms()) && cpConfig.getNeedRepaySms().equals(0)) {
                return;
            }
            VerifySmsCodeParam verifySmsCodeParam = new VerifySmsCodeParam();
            verifySmsCodeParam.setSmsCode(param.getSmsCode());
            verifySmsCodeParam.setOpenId(loanUserEntity.getOutOpenId());
            verifySmsCodeParam.setMobileNo(RequestUtils.getMobileNo());
            verifySmsCodeParam.setUserId(loanUserEntity.getOpenId());
            verifySmsCodeParam.setSerialNo(param.getSerialNo());
            verifySmsCodeParam.setSupplier(Integer.parseInt(RequestUtils.getSupplier()));
            verifySmsCodeParam.setType(REPAY_SMS_TYPE);
            VerifySmsCodeDto verifySmsCodeDto = loanClient.verifySmsCode(verifySmsCodeParam);
            if (!verifySmsCodeDto.getResult()) {
                throw new BusinessException(WalletResultCode.VERIFY_SMSCODE_ERROR);
            }
        }
    }

    /**
     * 发送短信
     */
    private String sendSmsCode(LoanUserEntity loanUserEntity, RepayCheckResponse repayCheckDto) {
        String sha256UserId = SHA256Utils.getSHA256Str(RequestUtils.getUid());
        String sha256MobileNo = SHA256Utils.getSHA256Str(RequestUtils.getMobileNo());
        smsService.checkSmsSendLimitForRepay(sha256UserId, sha256MobileNo, LoanConstant.SendSmsType.REPAY, repayCheckDto);
        if (Objects.nonNull(repayCheckDto.getStatus()) && repayCheckDto.getStatus().equals(GeneralConstant.VerificationCodeSendingResult.FAIL)) {
            return Strings.EMPTY;
        }
        SendSmsCodeForClientParam sendSmsCodeParam = new SendSmsCodeForClientParam();
        sendSmsCodeParam.setOpenId(loanUserEntity.getOutOpenId());
        sendSmsCodeParam.setSupplier(Integer.parseInt(RequestUtils.getSupplier()));
        sendSmsCodeParam.setUserId(loanUserEntity.getOpenId());
        sendSmsCodeParam.setMobileNo(RequestUtils.getMobileNo());
        sendSmsCodeParam.setType(REPAY_SMS_TYPE);
        LoanSupplierDto cpConfig = loanSupplierService.getCpConfig();
        if (!Objects.isNull(cpConfig.getNeedRepaySms()) && cpConfig.getNeedRepaySms().equals(0)) {
            return "";
        }
        String serialNo = loanClient.sendSmsCode(sendSmsCodeParam);
        smsService.refreshCache(sha256UserId, sha256MobileNo, LoanConstant.SendSmsType.REPAY);
        return serialNo;
    }

    /**
     * 参数校验
     *
     * @param repayParam RepayParam
     */
    private void checkRepayInfo(RepayParam repayParam) {
        if (repayParam.getRepayPart() && repayParam.getRepayType() == LoanConstant.RepayType.NORMAL) {
            if (!checkRepayTermsExist(repayParam.getRepayItemList())) {
                throw new BusinessException(WalletResultCode.VALIDATED_PARAM);
            }
        }
    }


    /**
     * 非空校验
     *
     * @param repayItemList repayItemList
     * @return boolean
     */
    private boolean checkRepayTermsExist(List<RepayItemParam> repayItemList) {
        // 使用Optional.ofNullable来优雅地处理可能为null的情况
        return Optional.ofNullable(repayItemList)
                .map(List::stream) // 将List转换为Stream
                .orElseGet(Stream::empty) // 如果List为null，则使用一个空的Stream
                .filter(Objects::nonNull) // 确保Stream中的元素不为null
                .allMatch(repayItem ->
                        Optional.ofNullable(repayItem.getRepayTerms()) // 使用Optional处理repayTerms
                                .map(terms -> !terms.isEmpty()) // 检查repayTerms是否非空
                                .orElse(false) // 如果repayTerms为null，则视为校验失败
                );
    }
}
