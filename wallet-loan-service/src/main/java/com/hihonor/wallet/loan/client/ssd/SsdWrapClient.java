package com.hihonor.wallet.loan.client.ssd;

import java.util.Collections;
import java.util.List;
import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.alipay.api.domain.AlipayPcreditLoanHonorBudgetQueryModel;
import com.alipay.api.domain.AlipayPcreditLoanHonorLenddetailQueryModel;
import com.alipay.api.domain.AlipayPcreditLoanHonorPlanBatchqueryModel;
import com.alipay.api.domain.AlipayPcreditLoanHonorRepayApplyModel;
import com.alipay.api.domain.AlipayPcreditLoanHonorRepayBatchqueryModel;
import com.alipay.api.domain.AlipayPcreditLoanHonorRepayresultQueryModel;
import com.alipay.api.domain.AlipayPcreditLoanHonorUsercancelaccountApplyModel;
import com.alipay.api.domain.AlipayPcreditLoanHonorUsercancelaccountCheckModel;
import com.alipay.api.request.AlipayPcreditLoanHonorBudgetQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorLenddetailQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorPlanBatchqueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayApplyRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayBatchqueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayresultQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorUsercancelaccountApplyRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorUsercancelaccountCheckRequest;
import com.alipay.api.response.AlipayPcreditLoanHonorBudgetQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorLenddetailQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorPlanBatchqueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayApplyResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayBatchqueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayresultQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorUsercancelaccountApplyResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorUsercancelaccountCheckResponse;
import com.hihonor.wallet.loan.client.BaseLoanService;
import com.hihonor.wallet.loan.client.constant.LoanClientConstant;
import com.hihonor.wallet.loan.client.model.dto.ApplyLimitDto;
import com.hihonor.wallet.loan.client.model.dto.BindBankcardDto;
import com.hihonor.wallet.loan.client.model.dto.BindUserDto;
import com.hihonor.wallet.loan.client.model.dto.CouponListDto;
import com.hihonor.wallet.loan.client.model.dto.IdCardCheckDto;
import com.hihonor.wallet.loan.client.model.dto.LoanApplyDto;
import com.hihonor.wallet.loan.client.model.dto.LoanCalculateDto;
import com.hihonor.wallet.loan.client.model.dto.LoanVerifyListDto;
import com.hihonor.wallet.loan.client.model.dto.MobileModifyDto;
import com.hihonor.wallet.loan.client.model.dto.QueryAdjustInfoDto;
import com.hihonor.wallet.loan.client.model.dto.QueryAllCouponListDto;
import com.hihonor.wallet.loan.client.model.dto.QueryBindBankcardListDto;
import com.hihonor.wallet.loan.client.model.dto.QueryCreditApplyResultDto;
import com.hihonor.wallet.loan.client.model.dto.QueryLoanCouponDto;
import com.hihonor.wallet.loan.client.model.dto.QueryLoanResultDto;
import com.hihonor.wallet.loan.client.model.dto.QueryRepayResultDto;
import com.hihonor.wallet.loan.client.model.dto.QuerySettlementDto;
import com.hihonor.wallet.loan.client.model.dto.QuerySupportBankcardListDto;
import com.hihonor.wallet.loan.client.model.dto.QueryUserCreditInfoDto;
import com.hihonor.wallet.loan.client.model.dto.RepayCalculateDto;
import com.hihonor.wallet.loan.client.model.dto.RepayDoDto;
import com.hihonor.wallet.loan.client.model.dto.ResignCheckDto;
import com.hihonor.wallet.loan.client.model.dto.SendSettlementDto;
import com.hihonor.wallet.loan.client.model.dto.TransferRepayDoDto;
import com.hihonor.wallet.loan.client.model.dto.UserCancelAccountDto;
import com.hihonor.wallet.loan.client.model.dto.UserLogoffDto;
import com.hihonor.wallet.loan.client.model.dto.VerifyBindSmsDto;
import com.hihonor.wallet.loan.client.model.dto.VerifySmsCodeDto;
import com.hihonor.wallet.loan.client.model.param.AllCouponListParam;
import com.hihonor.wallet.loan.client.model.param.ApplyLimitParam;
import com.hihonor.wallet.loan.client.model.param.BindBankcardParam;
import com.hihonor.wallet.loan.client.model.param.BindUserParam;
import com.hihonor.wallet.loan.client.model.param.CloseLoanOrderParam;
import com.hihonor.wallet.loan.client.model.param.IdCardCheckParam;
import com.hihonor.wallet.loan.client.model.param.LoanApplyParam;
import com.hihonor.wallet.loan.client.model.param.LoanCalculateParam;
import com.hihonor.wallet.loan.client.model.param.LoanVerifyListParam;
import com.hihonor.wallet.loan.client.model.param.MobileModifyParam;
import com.hihonor.wallet.loan.client.model.param.QueryAddCreditUrlParam;
import com.hihonor.wallet.loan.client.model.param.QueryAdjustInfoParam;
import com.hihonor.wallet.loan.client.model.param.QueryAgreementParam;
import com.hihonor.wallet.loan.client.model.param.QueryAllCouponListParam;
import com.hihonor.wallet.loan.client.model.param.QueryBindBankcardListParam;
import com.hihonor.wallet.loan.client.model.param.QueryContractDetailParam;
import com.hihonor.wallet.loan.client.model.param.QueryCreditApplyResultParam;
import com.hihonor.wallet.loan.client.model.param.QueryLoanCouponParam;
import com.hihonor.wallet.loan.client.model.param.QueryLoanRecordDetailParam;
import com.hihonor.wallet.loan.client.model.param.QueryLoanRecordParam;
import com.hihonor.wallet.loan.client.model.param.QueryLoanResultParam;
import com.hihonor.wallet.loan.client.model.param.QueryRepayPlanParam;
import com.hihonor.wallet.loan.client.model.param.QueryRepayRecordParam;
import com.hihonor.wallet.loan.client.model.param.QueryRepayResultParam;
import com.hihonor.wallet.loan.client.model.param.QuerySettlementParam;
import com.hihonor.wallet.loan.client.model.param.QuerySupportBankcardListParam;
import com.hihonor.wallet.loan.client.model.param.QueryUsableCounponsParam;
import com.hihonor.wallet.loan.client.model.param.QueryUserCreditInfoParam;
import com.hihonor.wallet.loan.client.model.param.RepayCalculateParam;
import com.hihonor.wallet.loan.client.model.param.RepayDoParam;
import com.hihonor.wallet.loan.client.model.param.ResignCheckParam;
import com.hihonor.wallet.loan.client.model.param.SendSettlementParam;
import com.hihonor.wallet.loan.client.model.param.SendSmsCodeForClientParam;
import com.hihonor.wallet.loan.client.model.param.TransferRepayDoParam;
import com.hihonor.wallet.loan.client.model.param.UserCancelAccountParam;
import com.hihonor.wallet.loan.client.model.param.UserLogoffParam;
import com.hihonor.wallet.loan.client.model.param.VerifyBindSmsParam;
import com.hihonor.wallet.loan.client.model.param.VerifySmsCodeParam;
import com.hihonor.wallet.loan.client.ssd.converter.repay.SsdRepayConverter;
import com.hihonor.wallet.loan.model.dto.ContractDetailDto;
import com.hihonor.wallet.loan.model.dto.ContractInfoDto;
import com.hihonor.wallet.loan.model.dto.LoanRecordDetailDto;
import com.hihonor.wallet.loan.model.dto.LoanRecordListDto;
import com.hihonor.wallet.loan.model.dto.QueryAddCreditUrlDto;
import com.hihonor.wallet.loan.model.dto.QueryWithholdSignUrlDto;
import com.hihonor.wallet.loan.model.dto.QueryWithholdSignUrlParam;
import com.hihonor.wallet.loan.model.dto.RepayDto;
import com.hihonor.wallet.loan.model.dto.RepayPlanDto;

import cn.hutool.core.convert.Convert;

/**
 * 随身贷客户端包装类
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
@Component(BaseLoanService.CLASS_NAME + LoanClientConstant.LoanTechProvider.SSD)
public class SsdWrapClient extends BaseLoanService{

    @Autowired
    private SsdApi ssdApi;

    @Autowired
    private SsdRepayConverter ssdRepayConverter;

    @Override
    public MobileModifyDto mobileModify(MobileModifyParam param) {
        return null;
    }

    @Override
    public BindUserDto bindUser(BindUserParam param) {
        return null;
    }

    @Override
    public QueryUserCreditInfoDto queryUserCreditInfo(QueryUserCreditInfoParam param) {
        return null;
    }

    @Override
    public QueryAdjustInfoDto queryAdjustInfo(QueryAdjustInfoParam param) {
        return null;
    }

    @Override
    public ApplyLimitDto applyLimit(ApplyLimitParam param) {
        return null;
    }

    @Override
    public QueryCreditApplyResultDto queryCreditApplyResult(QueryCreditApplyResultParam param) {
        return null;
    }

    @Override
    public LoanCalculateDto loanTrial(LoanCalculateParam param) {
        return null;
    }

    @Override
    public LoanVerifyListDto loanVerifyList(LoanVerifyListParam param) {
        return null;
    }

    @Override
    public LoanApplyDto loanApply(LoanApplyParam param) {
        return null;
    }

    @Override
    public QueryLoanResultDto queryLoanResult(QueryLoanResultParam param) {
        return null;
    }

    @Override
    public LoanRecordListDto queryLoanRecord(QueryLoanRecordParam param) {
        return null;
    }

    @Override
    public LoanRecordDetailDto queryLoanRecordDetail(QueryLoanRecordDetailParam param) {
        AlipayPcreditLoanHonorLenddetailQueryRequest request = new AlipayPcreditLoanHonorLenddetailQueryRequest();
        AlipayPcreditLoanHonorLenddetailQueryModel model = new AlipayPcreditLoanHonorLenddetailQueryModel();
        model.setChannelCustomerId(param.getUserId());
        model.setAlipayUserId(param.getOpenId());
        model.setApplyNo(param.getApplyNo());
        request.setBizModel(model);
        AlipayPcreditLoanHonorLenddetailQueryResponse response = ssdApi.queryLoanRecordDetail(request);
        return ssdRepayConverter.convertLoanRecordDetail(response);
    }

    @Override
    public Integer closeLoanOrder(CloseLoanOrderParam param) {
        return 0;
    }

    @Override
    public List<RepayPlanDto> queryRepayPlan(QueryRepayPlanParam param) {
        AlipayPcreditLoanHonorPlanBatchqueryRequest request = new AlipayPcreditLoanHonorPlanBatchqueryRequest();
        AlipayPcreditLoanHonorPlanBatchqueryModel model = new AlipayPcreditLoanHonorPlanBatchqueryModel();
        model.setChannelCustomerId(param.getUserId());
        model.setAlipayUserId(param.getOpenId());
        model.setApplyNo(param.getApplyNo());
        request.setBizModel(model);

        AlipayPcreditLoanHonorPlanBatchqueryResponse response = ssdApi.queryRepayPlan(request);

        return ssdRepayConverter.convertRepayPlanList(response.getRecords());
    }

    @Override
    public RepayCalculateDto repayCalculate(RepayCalculateParam param) {
        AlipayPcreditLoanHonorBudgetQueryRequest request = new AlipayPcreditLoanHonorBudgetQueryRequest();
        AlipayPcreditLoanHonorBudgetQueryModel model = new AlipayPcreditLoanHonorBudgetQueryModel();
        model.setChannelCustomerId(param.getUserId());
        model.setAlipayUserId(param.getOpenId());
        model.setOutOrderNo(param.getOutOrderNo());
        model.setRepayAmount(Convert.toStr(param.getRepayAmount()));
        model.setBudgetType(param.getBudgetType());
        request.setBizModel(model);

        AlipayPcreditLoanHonorBudgetQueryResponse response = ssdApi.repayCalculate(request);

        return ssdRepayConverter.convertTrialResponse(response);
    }

    @Override
    public ResignCheckDto resignCheck(ResignCheckParam param) {
        return null;
    }

    @Override
    public VerifyBindSmsDto verifyBindSms(VerifyBindSmsParam param) {
        return null;
    }

    @Override
    public RepayDoDto repayDo(RepayDoParam param) {
        AlipayPcreditLoanHonorRepayApplyRequest request = new AlipayPcreditLoanHonorRepayApplyRequest();
        AlipayPcreditLoanHonorRepayApplyModel model = new AlipayPcreditLoanHonorRepayApplyModel();
        model.setChannelCustomerId(param.getUserId());
        model.setAlipayUserId(param.getOpenId());
        model.setOutOrderNo(param.getTransNo());
        model.setRepayAmount(Convert.toStr(param.getTotalAmount()));
        model.setBudgetType(param.getBudgetType());
        model.setBudgetAmount(Convert.toStr(param.getBudgetAmount()));
        model.setBudgetInterest(Convert.toStr(param.getBudgetInterest()));
        model.setBudgetPrincipal(Convert.toStr(param.getBudgetPrincipal()));
        model.setBudgetPenalty(Convert.toStr(param.getBudgetPenalty()));
        request.setBizModel(model);

        AlipayPcreditLoanHonorRepayApplyResponse response = ssdApi.repayDo(request);

        RepayDoDto repayDoDto = new RepayDoDto();
        repayDoDto.setRepayOrderId(response.getOutRepayNo());
        repayDoDto.setOrderInfo(response.getOutRepayNo());
        return repayDoDto;
    }

    @Override
    public TransferRepayDoDto transferRepay(TransferRepayDoParam param) {
        return null;
    }

    @Override
    public QueryRepayResultDto queryRepayResult(QueryRepayResultParam param) {
        AlipayPcreditLoanHonorRepayresultQueryRequest request = new AlipayPcreditLoanHonorRepayresultQueryRequest();
        AlipayPcreditLoanHonorRepayresultQueryModel model = new AlipayPcreditLoanHonorRepayresultQueryModel();
        model.setChannelCustomerId(param.getUserId());
        model.setAlipayUserId(param.getOpenId());
        model.setRepayNo(param.getTransNo());
        request.setBizModel(model);
        AlipayPcreditLoanHonorRepayresultQueryResponse response = ssdApi.queryRepayResult(request);
        return ssdRepayConverter.convertRepayResult(response);
    }

    @Override
    public List<RepayDto> queryRepayRecord(QueryRepayRecordParam param) {
        AlipayPcreditLoanHonorRepayBatchqueryRequest request = new AlipayPcreditLoanHonorRepayBatchqueryRequest();
        AlipayPcreditLoanHonorRepayBatchqueryModel model = new AlipayPcreditLoanHonorRepayBatchqueryModel();
        model.setChannelCustomerId(param.getUserId());
        model.setAlipayUserId(param.getOpenId());
        model.setOutOrderNo(param.getLoanOrderId());
        request.setBizModel(model);
        AlipayPcreditLoanHonorRepayBatchqueryResponse response = ssdApi.queryRepayRecord(request);

        return ssdRepayConverter.convertRepayRecords(response.getRecords());
    }

    @Override
    public QuerySettlementDto querySettlement(QuerySettlementParam param) {
        return null;
    }

    @Override
    public SendSettlementDto sendSettlement(SendSettlementParam param) {
        return null;
    }

    @Override
    public QueryBindBankcardListDto queryBindBankcardList(QueryBindBankcardListParam param) {
        return null;
    }

    @Override
    public QuerySupportBankcardListDto querySupportBankcardList(QuerySupportBankcardListParam param) {
        return null;
    }

    @Override
    public BindBankcardDto bindBankcard(BindBankcardParam param) {
        return null;
    }

    @Override
    public List<ContractInfoDto> queryAgreement(QueryAgreementParam param) {
        return Collections.emptyList();
    }

    @Override
    public List<ContractDetailDto> queryContractDetail(QueryContractDetailParam param) {
        return Collections.emptyList();
    }

    @Override
    public QueryLoanCouponDto queryLoanCoupon(QueryLoanCouponParam param) {
        return null;
    }

    @Override
    public QueryAllCouponListDto queryAllCouponList(QueryAllCouponListParam param) {
        return null;
    }

    @Override
    public String sendSmsCode(SendSmsCodeForClientParam param) {
        return "";
    }

    @Override
    public VerifySmsCodeDto verifySmsCode(VerifySmsCodeParam param) {
        return null;
    }

    @Override
    public IdCardCheckDto idCardCheck(IdCardCheckParam param) {
        return null;
    }

    @Override
    public List<CouponListDto> allCouponList(AllCouponListParam param) {
        return Collections.emptyList();
    }

    @Override
    public List<CouponListDto> loanUsableCounpons(QueryUsableCounponsParam param) {
        return Collections.emptyList();
    }

    @Override
    public UserCancelAccountDto userCancelAccount(UserCancelAccountParam param) {
        UserCancelAccountDto userCancelAccountDto = new UserCancelAccountDto();
        AlipayPcreditLoanHonorUsercancelaccountCheckModel model =
            new AlipayPcreditLoanHonorUsercancelaccountCheckModel();
        model.setChannelCustomerId(param.getUserId());
        model.setAlipayUserId(param.getOpenId());
        AlipayPcreditLoanHonorUsercancelaccountCheckRequest request =
            new AlipayPcreditLoanHonorUsercancelaccountCheckRequest();
        request.setBizModel(model);
        AlipayPcreditLoanHonorUsercancelaccountCheckResponse response = ssdApi.userCancelAccount(request);

        userCancelAccountDto.setLogoffAllowed(response.getLogoffAllowed());
        userCancelAccountDto.setDesc(response.getMsg());
        return userCancelAccountDto;
    }

    @Override
    public UserLogoffDto userLogoff(UserLogoffParam param) {
        UserLogoffDto userLogoffDto = new UserLogoffDto();
        AlipayPcreditLoanHonorUsercancelaccountApplyModel model =
            new AlipayPcreditLoanHonorUsercancelaccountApplyModel();
        model.setApplyNo(UUID.randomUUID().toString().replaceAll("-", ""));
        model.setChannelCustomerId(param.getUserId());
        model.setAlipayUserId(param.getOpenId());
        AlipayPcreditLoanHonorUsercancelaccountApplyRequest request =
            new AlipayPcreditLoanHonorUsercancelaccountApplyRequest();
        request.setBizModel(model);

        AlipayPcreditLoanHonorUsercancelaccountApplyResponse response = ssdApi.userLogoff(request);

        userLogoffDto.setIsLogoff(response.getLogoffResult());
        userLogoffDto.setDesc(response.getLogoffErrDesc());
        return userLogoffDto;
    }

    @Override
    public QueryAddCreditUrlDto queryAddUrl(QueryAddCreditUrlParam param) {
        return null;
    }

    @Override
    public QueryWithholdSignUrlDto querySignUrl(QueryWithholdSignUrlParam param) {
        return null;
    }
}
