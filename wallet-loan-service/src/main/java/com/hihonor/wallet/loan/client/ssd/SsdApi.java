package com.hihonor.wallet.loan.client.ssd;

import java.util.Map;

import com.alipay.api.request.AlipayPcreditLoanHonorBudgetQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorLenddetailQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorPlanBatchqueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayApplyRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayBatchqueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorRepayresultQueryRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorUsercancelaccountApplyRequest;
import com.alipay.api.request.AlipayPcreditLoanHonorUsercancelaccountCheckRequest;
import com.alipay.api.response.AlipayPcreditLoanHonorBudgetQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorLenddetailQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorPlanBatchqueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayApplyResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayBatchqueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorRepayresultQueryResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorUsercancelaccountApplyResponse;
import com.alipay.api.response.AlipayPcreditLoanHonorUsercancelaccountCheckResponse;
import com.hihonor.wallet.loan.client.ssd.model.dto.SsdBaseDto;
import com.hihonor.wallet.loan.client.ssd.model.param.SsdBaseParam;

/**
 * 随身贷客户端接口
 *
 * <AUTHOR>
 * @since 2025-08-08
 */
public interface SsdApi {
    /**
     * 借款记录详情查询
     *
     * @param request request
     * @return AlipayPcreditLoanHonorLenddetailQueryResponse
     */
    AlipayPcreditLoanHonorLenddetailQueryResponse queryLoanRecordDetail(
        AlipayPcreditLoanHonorLenddetailQueryRequest request);

    /**
     * 还款计划查询
     *
     * @param request request
     * @return AlipayPcreditLoanHonorPlanBatchqueryResponse
     */
    AlipayPcreditLoanHonorPlanBatchqueryResponse queryRepayPlan(AlipayPcreditLoanHonorPlanBatchqueryRequest request);

    /**
     * 还款试算
     *
     * @param request request
     * @return AlipayPcreditLoanHonorBudgetQueryResponse
     */
    AlipayPcreditLoanHonorBudgetQueryResponse repayCalculate(AlipayPcreditLoanHonorBudgetQueryRequest request);

    /**
     * 还款申请
     *
     * @param request request
     * @return AlipayPcreditLoanHonorRepayApplyResponse
     */
    AlipayPcreditLoanHonorRepayApplyResponse repayDo(AlipayPcreditLoanHonorRepayApplyRequest request);

    /**
     * 还款结果查询
     *
     * @param request request
     * @return AlipayPcreditLoanHonorRepayresultQueryResponse
     */
    AlipayPcreditLoanHonorRepayresultQueryResponse queryRepayResult(
        AlipayPcreditLoanHonorRepayresultQueryRequest request);

    /**
     * 还款记录查询
     *
     * @param request request
     * @return AlipayPcreditLoanHonorRepayBatchqueryResponse
     */
    AlipayPcreditLoanHonorRepayBatchqueryResponse queryRepayRecord(
        AlipayPcreditLoanHonorRepayBatchqueryRequest request);

    /**
     * 用户注销检查
     *
     * @param request request
     * @return AlipayPcreditLoanHonorUsercancelaccountCheckResponse
     */
    AlipayPcreditLoanHonorUsercancelaccountCheckResponse userCancelAccount(
        AlipayPcreditLoanHonorUsercancelaccountCheckRequest request);

    /**
     * 用户注销
     *
     * @param request request
     * @return AlipayPcreditLoanHonorUsercancelaccountCheckResponse
     */
    AlipayPcreditLoanHonorUsercancelaccountApplyResponse userLogoff(
        AlipayPcreditLoanHonorUsercancelaccountApplyRequest request);

    /**
     * 回调用的 解密方法
     *
     * @param params params
     * @return SsdBaseParam
     */
    SsdBaseParam getEncryptDataForNotify(Map<String, String> params);

    /**
     * 签名
     *
     * @param dto dto
     * @return String
     */
    String sign(SsdBaseDto dto);
}
