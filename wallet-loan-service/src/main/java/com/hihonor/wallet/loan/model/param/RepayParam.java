/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.model.param;

import java.util.List;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

import com.hihonor.wallet.common.model.BaseRequest;
import com.hihonor.wallet.common.model.EncryptedBaseParam;

import lombok.Data;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-02
 */
@Data
public class RepayParam extends EncryptedBaseParam {
    /**
     * 总还款金额，单位：分
     */
    @NotNull
    @Min(0)
    private Long totalAmount;

    /**
     * 还款类型，1-提前还款，2-正常还款
     */
    private Integer repayType;

    /**
     * 是否部分还款
     */
    private Boolean repayPart;

    /**
     * 还款卡绑卡ID
     */
    private String bankCardId;

    /**
     * 短信验证码
     */
    private String smsCode;


    /**
     * 验证码序列号（云侧发送短信验证码有返回时需要）
     */
    private String serialNo;

    /**
     * 是否需要重新签约标识
     */
    private Boolean needResign;

    /**
     * 还款列表
     */
    private List<RepayItemParam> repayItemList;

    /**
     * 预算类型 (1--结清预算 2--逾期到期预算)
     */
    private String budgetType;

    /**
     * 预算时返回的总金额（还款试算不传金额时返回的总金额）
     */
    private Long budgetAmount;

    /**
     * 预算时返回的利息（还款试算不传金额时返回的利息）
     */
    private Long budgetInterest;

    /**
     * 预算时返回的本金（还款试算不传金额时返回的本金）
     */
    private Long budgetPrincipal;

    /**
     * 预算时返回的罚息（还款试算不传金额时返回的罚息）
     */
    private Long budgetPenalty;
}
