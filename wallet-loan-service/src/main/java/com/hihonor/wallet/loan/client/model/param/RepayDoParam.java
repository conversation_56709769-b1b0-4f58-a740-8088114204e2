/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.client.model.param;

import java.util.List;


import com.hihonor.wallet.loan.model.param.RepayItemParam;
import com.hihonor.wallet.loan.model.param.RepayTermParam;

import lombok.Data;
import lombok.ToString;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-02-28
 */
@Data
@ToString
public class RepayDoParam {
    /**
     * userid
     */
    private String userId;

    /**
     * 外部平台id
     */
    private String openId;

    /**
     * 外部订单号
     */
    private String transNo;

    /**
     * 手机号
     */
    private String mobileNo;

    /**
     * 总还款金额，单位：分
     */
    private Long totalAmount;

    /**
     * 还款类型，1-提前还款，2-正常还款
     */
    private Integer repayType;

    /**
     * 还款卡绑卡ID
     */
    private String bankCardId;

    /**
     * 短信验证码
     */
    private String smsCode;

    /**
     * 是否需要重新签约标识
     */
    private Boolean needResign;

    /**
     * 还款列表
     */
    private List<RepayItemParam> repayItemList;

    /**
     * 还款期次列表，主动还款必传，提前还款无需传
     * 试算返回的repayTerms
     */
    private List<RepayTermParam> repayTerms;

    /**
     * 服务商
     */
    private Integer supplier;

    private Boolean repayPart;
    
    /**
     * 预算类型 (1--结清预算 2--逾期到期预算)
     */
    private String budgetType;

    /**
     * 预算时返回的总金额（还款试算不传金额时返回的总金额）
     */
    private Long budgetAmount;

    /**
     * 预算时返回的利息（还款试算不传金额时返回的利息）
     */
    private Long budgetInterest;

    /**
     * 预算时返回的本金（还款试算不传金额时返回的本金）
     */
    private Long budgetPrincipal;

    /**
     * 预算时返回的罚息（还款试算不传金额时返回的罚息）
     */
    private Long budgetPenalty;
}
