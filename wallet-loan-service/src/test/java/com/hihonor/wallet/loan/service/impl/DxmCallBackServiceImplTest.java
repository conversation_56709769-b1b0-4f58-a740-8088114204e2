/*
 * Copyright (c) Honor Device Co., Ltd. 2024-2024. All rights reserved.
 */

package com.hihonor.wallet.loan.service.impl;

import java.lang.reflect.Field;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.Comparator;
import java.util.List;
import java.util.UUID;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.util.JsonUtils;
import com.hihonor.wallet.common.util.RsaKeyUtils;
import com.hihonor.wallet.common.util.RsaUtils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.loan.client.config.LoanClientConfigParam;
import com.hihonor.wallet.loan.client.dxm.DxmClient;
import com.hihonor.wallet.loan.client.dxm.DxmWrapClient;
import com.hihonor.wallet.loan.client.dxm.model.dto.DxmLoanRecordDetailDto;
import com.hihonor.wallet.loan.client.dxm.model.param.DxmBaseParam;
import com.hihonor.wallet.loan.client.dxm.model.param.DxmRepayResultNotifyParam;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.enums.PushMessageTypeEnums;
import com.hihonor.wallet.loan.service.DxmCallBackService;

import lombok.extern.slf4j.Slf4j;

/**
 * 功能描述
 *
 * <AUTHOR>
 * @since 2024-03-19
 */
@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class DxmCallBackServiceImplTest {

    @Autowired
    private RsaKeyUtils rsaKeyUtils;

    @Autowired
    DxmCallBackService dxmCallBackService;

    private String dxmPrivateKey = "MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAMrc71GbveoZef2y0Qj0YW3AygzZjTWFTb1oMqoGSgSLk9FKAOsGg0sABZMpPWl6WvQ44q0Uc1VOQBq440buJG8YEHtC45hDry2HHM7tei+zXvcezcK4OWCZl+jezVVKvqfXjgb1TuACSmLdyDGDITgHXhk2jyzdPXzffSguTAl1AgMBAAECgYEAwD1YCNT2xtY3tYPz9XFotpIgpsauu05E2j3Y7SiikeYXQRjI/++fEb7GiatmCAOsF42kuXIMzYUm2Gvm7PuVx4S6f0ARGwTdHbav3Yc9ey6UG5TUJxaWEtV/KvLLmIiFALkBZ6Sk7oXKNA0m4AnZ0GbBoV3YuqcO+45jANfgZg0CQQDxctL7b+WVSdDVm/RhCjr2bGyXTL5pXKZmxJvRt8jHSUUBdQtB0dOjWqXnVVmGpgV0V0uo9IrLyFRMyfzcBEy/AkEA1xbL2JE6Xe7VkKoo7t4lGyGIJ7/z4O45RVIvIzWH0Ft5T0kOV0FU27daboyLPSk0bUjx4xY0vFwB36sr5zJSywJAZE0X+l3EmNzeRS6ROZK/Zckq3zuBSlGzyrNYK6nWXXtBkFZoA8K/0XeBvfUFyNrFH0x4YMqdsgQZiQDL6zZaDwJBAIr2Amr/oOrxE+9r0BhLU0PNDS9JN6H5puyfZS7lNcKqZpyPwYEkB1QQiI9lo8u25FU6zHP/c8DB3MAJ0cqD27MCQQCKEpKhJ/8NtBmnFakDQdUcreNWjKOra26PUQ7x5ZOY5ywU2+Hrb35vtnZoYp8lHVTfQ9lSHSq7muMsf95/BMlX";
    @Test
    public void creditApplyNotify() throws Exception {
        String res = "{\"openId\" : \"ae5707553504488785f5b8c1f24d9018\"," +
                "\"applyNo\" : \"2403275571601401597848\",\"userId\" : \"832199029\",\"outOrderNo\" : \"7170367705043435520\"," +
                "\"applyResult\" : \"PASS\",\"refuseControlDays\" : 0,\"refuseInfo\" : { },\"limitType\" : \"CIRCLE\"," +
                "\"identity\" : \"OLD\",\"creditLimit\" : 10000000,\"originCreditLimit\" : 10000000,\"remainLimit\" : 10000000," +
                "\"limitExpireDate\" : \"20220101\",\"productInfo\" : [{\"earlyRepay\" : \"Y\",\"dayRate\" : \"0.06\",\"termNum\" : \"1;3\"," +
                "\"repayMethod\" : \"FIXED_INSTALLMENT\"}],\"originProductInfo\" : [{\"earlyRepay\" : \"Y\",\"dayRate\" : \"0.06\",\"termNum\" : \"1;3\"," +
                "\"repayMethod\" : \"FIXED_INSTALLMENT\"}],\"totalCreditLimit\" : \"500000\",\"totalAvailableLimit\" : \"500000\"," +
                "\"tempLimitInfo\" : {\"validTime\" : \"2023-07-20 14:55\",\"creditLimit\" : \"500000\",\"availableLimit\" : \"500000\" }}";
        String key = generateAesKey();
        DxmBaseParam notifyParam = new DxmBaseParam();
        notifyParam.setParams(paramsEncrypt(res, key));
        notifyParam.setKey(getEncryptKey(key, rsaKeyUtils.getPublicKey2048()));
        notifyParam.setVersion("1.0");
        notifyParam.setTimestamp(System.currentTimeMillis());
        notifyParam.setAppId("honor");
        notifyParam.setSign(sortParamAndSign(notifyParam));
        dxmCallBackService.creditApplyNotify(JsonUtils.toJson(notifyParam));
    }

    @Test
    public void creditApplyNotifyForReport() throws Exception {
        String res = "{\"openId\" : \"c6268aa5b9a72fc7675c2061799c275b\",\"applyNo\" : \"2407105468101401187855\",\"userId\" : \"832199029\",\"outOrderNo\" : \"7170367705043435520\",\"applyResult\" : \"PASS\",\"refuseControlDays\" : 0,\"refuseInfo\" : { },\"limitType\" : \"CIRCLE\",\"identity\" : \"OLD\",\"creditLimit\" : 10000000,\"originCreditLimit\" : 10000000,\"remainLimit\" : 10000000,\"limitExpireDate\" : \"20220101\",\"productInfo\" : [{\"earlyRepay\" : \"Y\",\"dayRate\" : \"0.06\",\"termNum\" : \"1;3\",\"repayMethod\" : \"FIXED_INSTALLMENT\"}],\"originProductInfo\" : [{\"earlyRepay\" : \"Y\",\"dayRate\" : \"0.06\",\"termNum\" : \"1;3\",\"repayMethod\" : \"FIXED_INSTALLMENT\"}],\"totalCreditLimit\" : \"500000\",\"totalAvailableLimit\" : \"500000\",\"tempLimitInfo\" : {\"validTime\" : \"2023-07-20 14:55\",\"creditLimit\" : \"500000\",\"availableLimit\" : \"500000\" }}";
        String key = generateAesKey();
        DxmBaseParam notifyParam = new DxmBaseParam();
        notifyParam.setParams(paramsEncrypt(res, key));
        notifyParam.setKey(getEncryptKey(key, rsaKeyUtils.getPublicKey2048()));
        notifyParam.setVersion("1.0");
        notifyParam.setTimestamp(System.currentTimeMillis());
        notifyParam.setAppId("honor");
        notifyParam.setSign(sortParamAndSign(notifyParam));
        dxmCallBackService.creditApplyNotify(JsonUtils.toJson(notifyParam));
    }

    @Test
    public void creditNotify() throws Exception {
        String res = "{\"userId\":\"110105198911020388\",\"openId\":\"ae5707553504488785f5b8c1f24d9018\",\"applyNo\":\"2403275571601401597848\",\"partnerApplyNo\":\"1708885099611187021\",\"newLimit\":20000000,\"oldLimit\":19650000,\"totalAmount\":20000000,\"remainLimit\":500000,\"newDayRate\":\"0.02\",\"oldDayRate\":\"0.06\",\"newApr\":\"20.2\",\"oldApr\":\"20.6\",\"limitChangeType\":\"1001\",\"rateChangeType\":\"2002\",\"tempLimitInfo\":{\"validTime\":\"2023-09-1723:59:59\",\"creditLimit\":10000000,\"availableLimit\":10000000}}";
        String key = generateAesKey();
        DxmBaseParam notifyParam = new DxmBaseParam();
        notifyParam.setParams(paramsEncrypt(res, key));
        notifyParam.setKey(getEncryptKey(key, rsaKeyUtils.getPublicKey2048()));
        notifyParam.setVersion("1.0");
        notifyParam.setTimestamp(System.currentTimeMillis());
        notifyParam.setAppId("honor");
        notifyParam.setSign(sortParamAndSign(notifyParam));
        dxmCallBackService.notifyCreditChangeStatus(JsonUtils.toJson(notifyParam));
    }


    @Test
    public void creditNotify01() throws Exception {
//        String res = "{\"userId\":\"110105198911020388\",\"openId\":\"ae5707553504488785f5b8c1f24d9018\",\"applyNo\":\"2403275571601401597848\",\"partnerApplyNo\":\"1708885099611187021\",\"newLimit\":20000000,\"oldLimit\":19650000,\"totalAmount\":20000000,\"remainLimit\":500000,\"newDayRate\":\"0.02\",\"oldDayRate\":\"0.06\",\"newApr\":\"20.2\",\"oldApr\":\"20.6\",\"limitChangeType\":\"1001\",\"rateChangeType\":\"2002\",\"tempLimitInfo\":{\"validTime\":\"2023-09-1723:59:59\",\"creditLimit\":10000000,\"availableLimit\":10000000},}";
        String res ="\n" +
                "{\n" +
                "  \"userId\": \"110105198911020388\",\n" +
                "  \"openId\": \"ae5707553504488785f5b8c1f24d9018\",\n" +
                "  \"applyNo\": \"2403275571601401597848\",\n" +
                "  \"partnerApplyNo\": \"1708885099611187021\",\n" +
                "  \"newLimit\": 20000000,\n" +
                "  \"oldLimit\": 19650000,\n" +
                "  \"totalAmount\": 20000000,\n" +
                "  \"remainLimit\": 500000,\n" +
                "  \"newDayRate\": \"0.02\",\n" +
                "  \"oldDayRate\": \"0.06\",\n" +
                "  \"newApr\": \"20.2\",\n" +
                "  \"oldApr\": \"20.6\",\n" +
                "  \"limitChangeType\": \"1001\",\n" +
                "  \"rateChangeType\": \"2002\",\n" +
                "  \"tempLimitInfo\": {\n" +
                "    \"validTime\": \"2023-09-17 23:59:59\",\n" +
                "    \"creditLimit\": 10000000,\n" +
                "    \"availableLimit\": 10000000\n" +
                "  },\n" +
                "  \"tempDayRate\": \"0.065\",\n" +
                "  \"tempApr\": \"23.4\",\n" +
                "  \"tempPriceDueTime\": \"2020-07-02 22:22:00\"\n" +
                "}";
        String key = generateAesKey();
        DxmBaseParam notifyParam = new DxmBaseParam();
        notifyParam.setParams(paramsEncrypt(res, key));
        notifyParam.setKey(getEncryptKey(key, rsaKeyUtils.getPublicKey2048()));
        notifyParam.setVersion("1.0");
        notifyParam.setTimestamp(System.currentTimeMillis());
        notifyParam.setAppId("honor");
        notifyParam.setSign(sortParamAndSign(notifyParam));
        dxmCallBackService.notifyCreditChangeStatus(JsonUtils.toJson(notifyParam));
    }

    @Test
    public void test() {
        List<DxmLoanRecordDetailDto> list = new ArrayList<>();
        DxmLoanRecordDetailDto dxmLoanRecordDetailDto = new DxmLoanRecordDetailDto();
        dxmLoanRecordDetailDto.setStatus("FAIL");
        list.add(dxmLoanRecordDetailDto);
        DxmRepayResultNotifyParam dxmRepayResultNotifyParam = new DxmRepayResultNotifyParam();
        dxmRepayResultNotifyParam.setRepayStatus("SUCCESS");
        dxmRepayResultNotifyParam.setRepaySource("OTHER");
        covertRepayPushTye(list, dxmRepayResultNotifyParam);
    }

    /**
     * 生成AESkey
     *
     * @return
     */
    private static String generateAesKey() {
        UUID uuid = UUID.randomUUID();
        String randomString = uuid.toString().replaceAll("-", "").substring(0, 16);
        return randomString;
    }


    /**
     * dxmData 处理
     *
     * @param dxmBaseParam dxmGeneralParam
     * @param loanClientConfigParam loanClientConfigParam
     * @return string
     */
    public String handleDxmData(DxmBaseParam dxmBaseParam, LoanClientConfigParam loanClientConfigParam) {
        String params = dxmBaseParam.getParams();
        String key = generateAesKey();
        try {
            dxmBaseParam.setTimestamp(System.currentTimeMillis());
            dxmBaseParam.setParams(paramsEncrypt(params, key));
            dxmBaseParam.setKey(getEncryptKey(key, loanClientConfigParam.getSecretKey()));
            dxmBaseParam.setSign(sortParamAndSign(dxmBaseParam));
        } catch (Exception e) {
            LogUtil.runSensitiveInfoLog("dxm encrypt params error {}", e);
        }
        return JsonUtils.toJson(dxmBaseParam);
    }

    /**
     * 请求参数加密
     */
    private static String paramsEncrypt(String data, String aesKey) throws Exception {
        // 构造加密方法
        Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
        SecretKeySpec key = new SecretKeySpec(aesKey.getBytes(), "AES");
        IvParameterSpec iv = new IvParameterSpec(aesKey.getBytes(), 0, cipher.getBlockSize());
        cipher.init(Cipher.ENCRYPT_MODE, key, iv);

        // 执行加密
        byte[] encryptRet = cipher.doFinal(data.getBytes());

        // 加密结果转小写16进制
        return byte2LowerHex(encryptRet);
    }

    /**
     * 字节转小写16进制
     */
    private static String byte2LowerHex(byte[] b) {
        // 转成16进制字符串
        StringBuilder hs = new StringBuilder();
        String tmp;
        for (byte value : b) {
            // 整数转成十六进制表示
            tmp = (Integer.toHexString(value & 0XFF));
            if (tmp.length() == 1) {
                hs.append("0").append(tmp);
            } else {
                hs.append(tmp);
            }
        }
        // 转小写
        return hs.toString().toLowerCase();
    }

    /**
     * 加密key
     *
     * @param aesKey    aesKey
     * @param publicKey publicKey
     * @return
     * @throws Exception
     */
    public static String getEncryptKey(String aesKey, String publicKey) throws Exception {
        // 先解码默认已经base64编码的公钥
        byte[] decodePubKey = base64DecodeRsaKey(publicKey);

        // 构造加密器
        KeyFactory factory = KeyFactory.getInstance("RSA");
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(decodePubKey);
        PublicKey pubKey = factory.generatePublic(keySpec);

        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, pubKey);

        // 执行加密
        byte[] encryptKey = cipher.doFinal(aesKey.getBytes());

        // 加密结果base64编码
        byte[] base64Code = base64Encode(encryptKey);
        return new String(base64Code);
    }
    /**
     * base64编码
     */
    private static byte[] base64Encode(byte[] byteArr) {
        return Base64.getEncoder().encode(byteArr);
    }
    /**
     * base64解码RSA 密钥
     */
    private static byte[] base64DecodeRsaKey(String str) {
        // ras密钥中常常包含换行，这里直接去掉
        String tmp = String.join("", Arrays.asList(str.split("\n")));
        LogUtil.runInfoLog("str{}", tmp);
        return base64Decode(tmp);
    }

    /**
     * base64解码
     */
    private static byte[] base64Decode(String str) {
        // ras密钥中常常包含换行，这里直接去掉
        String tmp = String.join("", Arrays.asList(str.split("\n")));
        return Base64.getDecoder().decode(tmp);
    }

    /**
     * 排序参数并签名
     *
     * @param jsonObject 请求参数
     */
    public String sortParamAndSign(Object jsonObject) {
        try {
            String signValue = getSign(jsonObject);
            return RsaUtils.sign(signValue, dxmPrivateKey, RsaUtils.SIGN_ALGORITHMS, false);
        } catch (Exception e) {
            LogUtil.runErrorLog("sort params/sign params error : {}", e.getMessage());
        }
        return null;
    }

    /**
     * 根据传入变量拼接签名字段
     *
     * @param jsonObject Object
     * @return sign
     */
    private String getSign(Object jsonObject) {
        LogUtil.runSensitiveInfoLog("需要整理的参数为:{}", jsonObject);
        // 转换对象为字符串数组并排序
        String[] paramStrings = convertObjectToStringArray(jsonObject);
        Arrays.sort(paramStrings, new DxmClient.AsciiComparator());
        // 连接字符串数组并输出
        String signValue = String.join("&", paramStrings);
        LogUtil.runSensitiveInfoLog("度小满原始签名字段:{}", signValue);
        return signValue;
    }

    /**
     * 将数据转化成 参数=参数值的数组
     *
     * @param obj obj
     * @return String[]
     */
    public static String[] convertObjectToStringArray(Object obj) {
        List<String> paramStringList = new ArrayList<>();
        // 获取对象的所有字段，包括父类的字段
        Field[] fields = getAllFields(obj.getClass());
        // 遍历字段
        for (Field field : fields) {
            field.setAccessible(true);

            try {
                // 获取字段名称和值
                String fieldName = field.getName();
                Object fieldValue = field.get(obj);

                if (fieldValue == null || fieldName.equals("sign")) {
                    continue;
                }

                // 将字段名称和值拼接为参数名称=参数值的形式
                String value;
                if (fieldValue instanceof String) {
                    value = fieldValue.toString();
                } else {
                    value = JsonUtils.toJson(fieldValue);
                }
                String paramString = fieldName + "=" + value;

                // 将参数字符串添加到列表中
                paramStringList.add(paramString);
            } catch (IllegalAccessException e) {
                LogUtil.runErrorLog("数据转化异常", e);
            }
        }

        // 将列表转换为字符串数组
        String[] paramStrings = new String[paramStringList.size()];
        paramStringList.toArray(paramStrings);

        return paramStrings;
    }


    // ASCII码增序比较器
    public static class AsciiComparator implements Comparator<String> {
        @Override
        public int compare(String s1, String s2) {
            for (int i = 0; i < Math.min(s1.length(), s2.length()); i++) {
                char c1 = s1.charAt(i);
                char c2 = s2.charAt(i);
                if (c1 != c2) {
                    return c1 - c2;
                }
            }
            return s1.length() - s2.length();
        }
    }

    // 获取类及其父类的所有字段
    @SuppressWarnings("checkstyle:ParameterAssignment")
    private static Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();

        while (clazz != null) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        // 过滤合成成员
        fields.removeIf(Field::isSynthetic);
        return fields.toArray(new Field[0]);
    }

    /**
     * 还款push Type 转换
     *
     * @param detailDtoList detailDtoList
     * @param param param
     */
    private void covertRepayPushTye(List<DxmLoanRecordDetailDto> detailDtoList, DxmRepayResultNotifyParam param) {
        if (CollectionUtils.isEmpty(detailDtoList)) {
            return;
        }
        DxmLoanRecordDetailDto dto = detailDtoList.get(0);
        String type;
        LogUtil.runInfoLog("开始 还款push处理");
        if (StringUtils.equals("OTHER", param.getRepaySource())) {
            if ((DxmWrapClient.covertRepayStatus(param.getRepayStatus()) == LoanConstant.RepayStatus.REPAY_SUCCESS
                    || DxmWrapClient.covertRepayStatus(param.getRepayStatus()) == LoanConstant.RepayStatus.REPAY_PART_SUCCESS)
                    && "PAYOFF".equals(dto.getStatus())) {
                type = PushMessageTypeEnums.SuccessfulWithHolding.getStringValue();
                LogUtil.runSensitiveInfoLog("{} ---type", type);
            } else if (LoanConstant.RepayStatus.REPAY_SUCCESS == DxmWrapClient.covertRepayStatus(param.getRepayStatus())
                    || LoanConstant.RepayStatus.REPAY_PART_SUCCESS == DxmWrapClient.covertRepayStatus(param.getRepayStatus())) {
                type = PushMessageTypeEnums.SuccessfulWithHolding.getStringValue();
            } else {
                type = PushMessageTypeEnums.WithHoldingFailure.getStringValue();
            }
        } else {
            if (LoanConstant.RepayStatus.REPAY_SUCCESS == DxmWrapClient.covertRepayStatus(param.getRepayStatus())
                    || LoanConstant.RepayStatus.REPAY_PART_SUCCESS == DxmWrapClient.covertRepayStatus(param.getRepayStatus())) {
                type = PushMessageTypeEnums.RepaymentSuccess.getStringValue();
            } else {
                type = PushMessageTypeEnums.RepaymentFailure.getStringValue();
            }
        }

        if ("PAYOFF".equals(dto.getStatus())) {
            type = PushMessageTypeEnums.SuccessfulSettlement.getStringValue();
        }
        LogUtil.runSensitiveInfoLog("{} ---type", type);
    }


}