package com.hihonor.wallet.loan.service.impl;

import com.hihonor.gis.sdk.pojo.GisIpChinaRegion;
import com.hihonor.gis.sdk.service.GisIpService;
import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.client.DmpClient;
import com.hihonor.wallet.common.feign.config.ConfigFeign;
import com.hihonor.wallet.common.model.BaseHeader;
import com.hihonor.wallet.common.model.BaseRequest;
import com.hihonor.wallet.common.model.dto.config.AppSettingDto;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.common.util.EncryptUtil;
import com.hihonor.wallet.common.util.IpUtils;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.common.util.log.LogUtil;
import com.hihonor.wallet.common.util.log.SensitiveLog;
import com.hihonor.wallet.loan.enums.PushMessageTypeEnums;
import com.hihonor.wallet.loan.service.PushMessageService;
import com.hihonor.wallet.loan.util.ModelScoreUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.mockStatic;

@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class PushMessageServiceTest {
    MockedStatic<RequestUtils> requestUtils;

    @Autowired
    ConfigFeign configFeign;

    @Autowired
    PushMessageService pushMessageService;

    @Autowired
    private DmpClient dmpClient;

    @Autowired
    private ModelScoreUtils modelScoreUtils;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private GisIpService gisIpService;

    private static final String PHONE_PREFIX = "0086";

    @Before
    public void setUp() {
        BaseHeader header = new BaseHeader();
        requestUtils = mockStatic(RequestUtils.class);
        header.setPackageName("com.hihonor.health");
        header.setDeviceModel("PGT-AN10");
        header.setCid("1");
        header.setHnWalletEnable(false);
        header.setVersionCode(1);
        requestUtils.when(() -> RequestUtils.getUid()).thenReturn("6160086500000119599");
        requestUtils.when(() -> RequestUtils.getBaseHeader()).thenReturn(header);
        AppSettingDto appSettingDto = new AppSettingDto();
//        Mockito.when(configFeign.query(any())).thenReturn(appSettingDto);
        BaseRequest request = new BaseRequest();
    }

    @Test
    public void testDmpClient(){
        LogUtil.runInfoLog(EncryptUtil.encrypt("18681514258"));
    }

    @Test
    public void pushMessage() {
        Map map = new HashMap<>();
        map.put("limit", 100000);
        map.put("annualRate", 10);
        pushMessageService.pushMessage("6160086500000119599", PushMessageTypeEnums.CreditSuccess.getStringValue(), map);
        pushMessageService.pushMessage("6160086500000119599", PushMessageTypeEnums.CreditFailure.getStringValue(), null);
        pushMessageService.pushMessage("6160086500000119599", PushMessageTypeEnums.LetterFailure.getStringValue(), null);
        Map loanSuccessMap = new HashMap<>();
        loanSuccessMap.put("dueRepayDate", "1月1日");
        pushMessageService.pushMessage("6160086500000119599", PushMessageTypeEnums.LoanSuccess.getStringValue(), loanSuccessMap);
        Map RepaymentSuccessMap = new HashMap<>();
        RepaymentSuccessMap.put("repaymentAmount", "1000");
        RepaymentSuccessMap.put("principalBalance", "1000");
        pushMessageService.pushMessage("6160086500000119599", PushMessageTypeEnums.RepaymentSuccess.getStringValue(), RepaymentSuccessMap);
        pushMessageService.pushMessage("6160086500000119599", PushMessageTypeEnums.RepaymentFailure.getStringValue(), null);
        Map SuccessfulSettlementMap = new HashMap();
        SuccessfulSettlementMap.put("Amount", "1000");
        SuccessfulSettlementMap.put("limit", "99000");
        pushMessageService.pushMessage("6160086500000119599", PushMessageTypeEnums.SuccessfulSettlement.getStringValue(), new HashMap<>());
    }

    @Test
    public void test() {
        LogUtil.runInfoLog(SensitiveLog.hideMarkLog("\"couponNo\":\"9059772467\""));
    }

    @Test
    public void test1() {
        GisIpChinaRegion chinaCity = gisIpService.getChinaCity("*************");
        System.out.println(chinaCity.getCity());
    }
}
