package com.hihonor.wallet.loan.service.impl;

import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.redis.RedisUtil;
import com.hihonor.wallet.loan.entity.LoanAdActivityEntity;
import com.hihonor.wallet.loan.mapper.LoanAdActivityMapper;
import com.hihonor.wallet.loan.model.dto.LoanAdActivityQueryDto;
import com.hihonor.wallet.loan.model.param.LoanActivityQueryParam;
import com.hihonor.wallet.loan.service.impl.LoanAdActivityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class LoanAdActivityServiceImplTest {

    @Autowired
    private LoanAdActivityServiceImpl loanAdActivityServiceImpl;

    @MockBean
    private RedisUtil redisUtil;

    @MockBean
    private LoanAdActivityMapper loanAdActivityMapper;

    @Before
    public void setup() {
        // 初始化Mock对象
    }

    @Test
    public void getLoanAdActivityConfig() {
        // 准备测试数据
        LoanActivityQueryParam param = new LoanActivityQueryParam();
        param.setActivityCode("activityCode");

        // 模拟redisUtil的行为
        LoanAdActivityEntity loanAdActivity = new LoanAdActivityEntity();
        loanAdActivity.setId(1);
        loanAdActivity.setActivityCode("ACTIVITY_CODE");
        loanAdActivity.setActivityDesc("ACTIVITY_DESC");
        loanAdActivity.setBackPicUrl("BACK_PIC_URL");
        loanAdActivity.setButtonImageUrl("BUTTON_IMAGE_URL");
        loanAdActivity.setLabelImageUrl("LABEL_IMAGE_URL");
        loanAdActivity.setButtonHandIconUrl("BUTTON_HAND_ICON_URL");
        loanAdActivity.setLoginPromptText("LOGIN_PROMPT_TEXT");
        loanAdActivity.setLoginButtonImageUrl("LOGIN_BUTTON_IMAGE_URL");
        loanAdActivity.setLoginBackPicUrl("LOGIN_BACK_PIC_URL");
        loanAdActivity.setPreDivisionSupplier("1");
        loanAdActivity.setPageLink("PAGE_LINK");
        loanAdActivity.setCid("CID");
        loanAdActivity.setSubCid("SUB_CID");
        loanAdActivity.setOperId("OPER_ID");
        loanAdActivity.setEndTime(new Date());
        loanAdActivity.setCreateTime(new Date());
        loanAdActivity.setUpdateTime(new Date());

        when(redisUtil.get(anyString(), eq(LoanAdActivityEntity.class))).thenReturn(loanAdActivity);

        // 执行测试方法
        LoanAdActivityQueryDto result = loanAdActivityServiceImpl.getLoanAdActivityConfig(param);
        log.info("result --- {}",result);

        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void getLoanAdActivityConfigCacheMiss() {
        // 准备测试数据
        LoanActivityQueryParam param = new LoanActivityQueryParam();
        param.setActivityCode("activityCode");

        // 模拟redisUtil的行为
        when(redisUtil.get(anyString(), eq(LoanAdActivityEntity.class))).thenReturn(null);

        // 模拟loanAdActivityMapper的行为
        LoanAdActivityEntity loanAdActivity = new LoanAdActivityEntity();
        loanAdActivity.setId(1);
        loanAdActivity.setActivityCode("ACTIVITY_CODE");
        loanAdActivity.setActivityDesc("ACTIVITY_DESC");
        loanAdActivity.setBackPicUrl("BACK_PIC_URL");
        loanAdActivity.setButtonImageUrl("BUTTON_IMAGE_URL");
        loanAdActivity.setLabelImageUrl("LABEL_IMAGE_URL");
        loanAdActivity.setButtonHandIconUrl("BUTTON_HAND_ICON_URL");
        loanAdActivity.setLoginPromptText("LOGIN_PROMPT_TEXT");
        loanAdActivity.setLoginButtonImageUrl("LOGIN_BUTTON_IMAGE_URL");
        loanAdActivity.setLoginBackPicUrl("LOGIN_BACK_PIC_URL");
        loanAdActivity.setPreDivisionSupplier("1");
        loanAdActivity.setPageLink("PAGE_LINK");
        loanAdActivity.setCid("CID");
        loanAdActivity.setSubCid("SUB_CID");
        loanAdActivity.setOperId("OPER_ID");
        loanAdActivity.setEndTime(new Date());
        loanAdActivity.setCreateTime(new Date());
        loanAdActivity.setUpdateTime(new Date());
        when(loanAdActivityMapper.selectByActivityCode(anyString())).thenReturn(loanAdActivity);

        // 执行测试方法
        LoanAdActivityQueryDto result = loanAdActivityServiceImpl.getLoanAdActivityConfig(param);
        log.info("result --- {}",result);
        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void getLoanAdActivityConfigActivityCodeNotExist() {
        // 准备测试数据
        LoanActivityQueryParam param = new LoanActivityQueryParam();
        param.setActivityCode("activityCode");

        // 模拟redisUtil的行为
        when(redisUtil.get(anyString(), eq(LoanAdActivityEntity.class))).thenReturn(null);

        // 模拟loanAdActivityMapper的行为
        when(loanAdActivityMapper.selectByActivityCode(anyString())).thenReturn(null);

        // 执行测试方法
        LoanAdActivityQueryDto result = loanAdActivityServiceImpl.getLoanAdActivityConfig(param);
        log.info("result --- {}",result);
        // 断言结果
        assertNull(result);
    }
}
