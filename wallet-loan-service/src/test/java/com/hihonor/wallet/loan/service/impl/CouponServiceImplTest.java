package com.hihonor.wallet.loan.service.impl;

import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.model.BaseHeader;
import com.hihonor.wallet.common.util.IdUtils;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.loan.client.LoanClient;
import com.hihonor.wallet.loan.client.model.dto.CouponListDto;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.CreditApplyEntity;
import com.hihonor.wallet.loan.entity.LoanUserEntity;
import com.hihonor.wallet.loan.mapper.CreditApplyMapper;
import com.hihonor.wallet.loan.model.dto.BandCardCouponDto;
import com.hihonor.wallet.loan.model.dto.BandCardCouponUsableDto;
import com.hihonor.wallet.loan.service.UserService;
import com.hihonor.wallet.loan.model.param.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class CouponServiceImplTest {

    @Autowired
    private CouponServiceImpl couponServiceImpl;

    @MockBean
    private UserService userService;

    @MockBean
    private CreditApplyMapper creditApplyMapper;

    @MockBean
    private LoanClient loanClient;
    MockedStatic<RequestUtils> requestUtils;

    private static final String userId = "855008123456119796";
    private static final Long userIdLong = 855008123456119796L;
    private static final Long userIdLong2 = 855008123456119797L;
    private static final String openId = "123456789987111";
    private static final String mobile = "123456789987111";
    private static final String outOrderNo = "123456789123";
    private static final String outOpenId = "442ba8a6b5725f1b76ad5a2c326717cc36cb3fa7";
    private static final String applyNo = IdUtils.generateCreditOrderId(userId, 1);

    @Before
    public void setUp() {
        BaseHeader header = new BaseHeader();
        requestUtils = mockStatic(RequestUtils.class);
        header.setPackageName("com.hihonor.health");
        header.setDeviceModel("PGT-AN10");
        header.setCid("1");
        header.setHnWalletEnable(false);
        header.setVersionCode(1);
        header.setSubCid("1");
        header.setLoc("1");
        header.setCid("");
        requestUtils.when(() -> RequestUtils.getUid()).thenReturn("6160086500000119599");
        requestUtils.when(() -> RequestUtils.getSupplier()).thenReturn("1");
        requestUtils.when(() -> RequestUtils.getMobileNo()).thenReturn(mobile);
        requestUtils.when(() -> RequestUtils.getBaseHeader()).thenReturn(header);
    }

    @After
    public void tearDown() {
        requestUtils.close();
    }

    @Test
    public void couponList() {
        // 准备测试数据
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(1l);
        loanUserEntity.setSupplier(1);
        loanUserEntity.setOutOpenId(outOpenId);

        // 模拟userService的行为
        Mockito.when(userService.getUserAccessInfoV2()).thenReturn(loanUserEntity);

        // 模拟creditApplyMapper的行为
        CreditApplyEntity creditApplyEntity = new CreditApplyEntity();
        creditApplyEntity.setUserId(1l);
        creditApplyEntity.setSupplier(1);
        creditApplyEntity.setApplyStatus(LoanConstant.CreditStatus.APPROVED);
        when(creditApplyMapper.selectOne(any())).thenReturn(creditApplyEntity);

        // 模拟loanClient的行为
        List<CouponListDto> couponListDtos = new ArrayList<>();
        when(loanClient.allCouponList(any())).thenReturn(couponListDtos);

        // 执行测试方法
        List<BandCardCouponDto> result = couponServiceImpl.couponList();

        // 断言结果
        assertNotNull(result);
        assertEquals(couponListDtos.size(), result.size());
    }

    @Test
    public void couponListUserInfoIsNull() {
        // 准备测试数据
        when(userService.getUserAccessInfoV2()).thenReturn(null);

        // 执行测试方法
        List<BandCardCouponDto> result = couponServiceImpl.couponList();

        // 断言结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void couponListCreditApplyEntityIsNull() {
        // 准备测试数据
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(1l);
        loanUserEntity.setSupplier(1);
        loanUserEntity.setOutOpenId(outOpenId);
        when(userService.getUserAccessInfoV2()).thenReturn(loanUserEntity);

        // 模拟creditApplyMapper的行为
        when(creditApplyMapper.selectOne(any())).thenReturn(null);

        // 执行测试方法
        List<BandCardCouponDto> result = couponServiceImpl.couponList();

        // 断言结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }


    @Test
    public void loanUsableCounpons() {
        // 准备测试数据
        LoanUsableCounponsParam param = new LoanUsableCounponsParam();
        param.setLoanAmount(1000l);
        param.setTotalTerm(12);
        param.setRepayMethod(1);

        // 模拟userService的行为
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setSupplier(1);
        loanUserEntity.setOutOpenId(outOpenId);
        loanUserEntity.setOpenId(openId);
        when(userService.getUserAccessInfo()).thenReturn(loanUserEntity);

        // 模拟loanClient的行为
        List<CouponListDto> couponListDtos = new ArrayList<>();
        when(loanClient.loanUsableCounpons(any())).thenReturn(couponListDtos);

        // 执行测试方法
        List<BandCardCouponUsableDto> result = couponServiceImpl.loanUsableCounpons(param);

        // 断言结果
        assertEquals(couponListDtos.size(), result.size());
    }

    @Test
    public void loanUsableCounponsNullUserService() {

        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(userIdLong);
        loanUserEntity.setSupplier(5);
        loanUserEntity.setAccessResult(2);
        loanUserEntity.setCid("");
        loanUserEntity.setDeviceModel("PGT-AN00");
        loanUserEntity.setOpenId(openId);
        loanUserEntity.setMobileNo(mobile);
        loanUserEntity.setOutOpenId(openId);
        Mockito.when(userService.getUserAccessInfo()).thenReturn(loanUserEntity);
        // 准备测试数据
        LoanUsableCounponsParam param = new LoanUsableCounponsParam();
        param.setLoanAmount(1000l);
        param.setTotalTerm(12);
        param.setRepayMethod(1);

        // 模拟userService的行为
        when(userService.getUserAccessInfo()).thenReturn(null);

        // 执行测试方法
        try {
            couponServiceImpl.loanUsableCounpons(param);
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // 断言结果
        }
    }

    @Test
    public void loanUsableCounponsNullLoanClient() {
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(userIdLong);
        loanUserEntity.setSupplier(5);
        loanUserEntity.setAccessResult(2);
        loanUserEntity.setCid("");
        loanUserEntity.setDeviceModel("PGT-AN00");
        loanUserEntity.setOpenId(openId);
        loanUserEntity.setMobileNo(mobile);
        loanUserEntity.setOutOpenId(openId);
        Mockito.when(userService.getUserAccessInfo()).thenReturn(loanUserEntity);
        // 准备测试数据
        LoanUsableCounponsParam param = new LoanUsableCounponsParam();
        param.setLoanAmount(1000l);
        param.setTotalTerm(12);
        param.setRepayMethod(1);


        // 模拟loanClient的行为
        when(loanClient.loanUsableCounpons(any())).thenThrow(new NullPointerException());

        // 执行测试方法
        try {
            couponServiceImpl.loanUsableCounpons(param);
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // 断言结果
        }
    }
}
