package com.hihonor.wallet.loan.service.impl;

import com.hihonor.wallet.WalletLoanApplication;
import com.hihonor.wallet.common.model.BaseHeader;
import com.hihonor.wallet.common.util.IdUtils;
import com.hihonor.wallet.common.util.RequestUtils;
import com.hihonor.wallet.loan.client.LoanClient;
import com.hihonor.wallet.loan.client.model.dto.IdCardCheckDto;
import com.hihonor.wallet.loan.constant.LoanConstant;
import com.hihonor.wallet.loan.entity.LoanUserEntity;
import com.hihonor.wallet.loan.model.dto.IdCardCheckResponse;
import com.hihonor.wallet.loan.model.dto.LoanSupplierDto;
import com.hihonor.wallet.loan.model.param.IdCardContentParam;
import com.hihonor.wallet.loan.service.UserService;
import com.hihonor.wallet.loan.service.impl.IdCardCheckServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@SpringBootTest(classes = WalletLoanApplication.class)
@ActiveProfiles("dev")
@RunWith(SpringRunner.class)
@Slf4j
public class IdCardCheckServiceImplTest {

    @Autowired
    private IdCardCheckServiceImpl idCardCheckServiceImpl;

    @MockBean
    private UserService userService;

    @MockBean
    private LoanClient loanClient;

    private static final String userId = "855008123456119796";
    private static final Long userIdLong = 855008123456119796L;
    private static final Long userIdLong2 = 855008123456119797L;
    private static final String openId = "123456789987111";
    private static final String mobile = "123456789987111";
    private static final String outOrderNo = "123456789123";
    private static final String outOpenId = "442ba8a6b5725f1b76ad5a2c326717cc36cb3fa7";
    private static final String applyNo = IdUtils.generateCreditOrderId(userId, 1);

    @Before
    public void setUp() {
        BaseHeader header = new BaseHeader();
       
        header.setPackageName("com.hihonor.health");
        header.setDeviceModel("PGT-AN10");
        header.setCid("1");
        header.setHnWalletEnable(false);
        header.setVersionCode(1);
        header.setSubCid("1");
        header.setLoc("1");
        header.setCid("");
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setUserId(userIdLong);
        loanUserEntity.setSupplier(1);
        loanUserEntity.setAccessResult(2);
        loanUserEntity.setCid("");
        loanUserEntity.setDeviceModel("PGT-AN00");
        loanUserEntity.setOpenId(openId);
        loanUserEntity.setMobileNo(mobile);
        loanUserEntity.setOutOpenId(openId);
        loanUserEntity.setAccessResult(LoanConstant.UserAccessResult.APPROVE);
        Mockito.when(userService.getUserAccessInfo()).thenReturn(loanUserEntity);
    }


    @Test
    public void idCardCheck() {
        // 准备测试数据
        IdCardContentParam param = new IdCardContentParam();
        param.setFaceImageContent("faceImageContent");
        param.setBackImageContent("backImageContent");

        // 模拟userService的行为
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setSupplier(5);
        loanUserEntity.setOutOpenId(outOpenId);
        when(userService.getUserAccessInfo()).thenReturn(loanUserEntity);

        // 模拟userService的行为
        LoanSupplierDto loanSupplierDto = new LoanSupplierDto();
        loanSupplierDto.setProvideIdVerficationMethod(LoanConstant.SupportIdCardCheck.SUPPORT);
        when(userService.getCurrentSupplierInfo(any())).thenReturn(loanSupplierDto);

        // 模拟loanClient的行为
        IdCardCheckDto dto = new IdCardCheckDto();
        when(loanClient.idCardCheck(any())).thenReturn(dto);

        // 执行测试方法
        IdCardCheckResponse result = idCardCheckServiceImpl.idCardCheck(param);
        log.info("result --- {}",result);

        // 断言结果
        assertNotNull(result);
        assertEquals(dto.getBackImage(), result.getBackImage());
        assertEquals(dto.getFrontImage(), result.getFrontImage());
    }

    @Test
    public void idCardCheckNotSupport() {
        // 准备测试数据
        IdCardContentParam param = new IdCardContentParam();
        param.setFaceImageContent("faceImageContent");
        param.setBackImageContent("backImageContent");

        // 模拟userService的行为
        LoanUserEntity loanUserEntity = new LoanUserEntity();
        loanUserEntity.setSupplier(5);
        loanUserEntity.setOutOpenId(outOpenId);
        when(userService.getUserAccessInfo()).thenReturn(loanUserEntity);

        // 模拟userService的行为
        LoanSupplierDto loanSupplierDto = new LoanSupplierDto();
        loanSupplierDto.setProvideIdVerficationMethod(LoanConstant.SupportIdCardCheck.NOT_SUPPORT);
        when(userService.getCurrentSupplierInfo(any())).thenReturn(loanSupplierDto);

        // 执行测试方法
        IdCardCheckResponse result = idCardCheckServiceImpl.idCardCheck(param);
        log.info("result --- {}",result);

        // 断言结果
        assertNotNull(result);
    }

    @Test
    public void idCardCheckUserInfoIsNull() {
        // 准备测试数据
        IdCardContentParam param = new IdCardContentParam();
        param.setFaceImageContent("faceImageContent");
        param.setBackImageContent("backImageContent");

        // 模拟userService的行为
        when(userService.getUserAccessInfo()).thenReturn(null);

        // 执行测试方法
        try {
            IdCardCheckResponse result = idCardCheckServiceImpl.idCardCheck(param);
            log.info("result --- {}",result);
            fail("Expected NullPointerException");
        } catch (NullPointerException e) {
            // 断言结果
        }
    }
}
