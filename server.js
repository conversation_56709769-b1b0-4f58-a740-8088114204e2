const express = require('express');
const { createServer } = require('http');
const { Server } = require('socket.io');
const cors = require('cors');
require('dotenv').config();

const app = express();
const server = createServer(app);

// 配置CORS
app.use(cors({
	origin: "*",
	credentials: true
}));

// 创建Socket.IO服务器
const io = new Server(server, {
	path: '/mock-websocket',
	cors: {
		origin: "*",
		methods: ["GET", "POST"],
		credentials: true
	}
});

// WebSocket服务器逻辑
class WebSocketServer {
	constructor(io) {
		this.io = io;
		this.users = new Map();
		this.fileContents = new Map();
		this.cursors = new Map();
		this.connectionCount = 0;
		this.setupEventHandlers();
	}

	setupEventHandlers() {
		this.io.on('connection', (socket) => {
			this.connectionCount++;
			console.log(`用户连接: ${socket.id} (总连接数: ${this.connectionCount})`);

			// 加入会话
			socket.on('join-session', (data) => {
				const { sessionId, username } = data;
				this.users.set(socket.id, {
					id: socket.id,
					username,
					sessionId,
					fileId: null
				});

				// 加入房间
				socket.join(sessionId);

				// 通知其他用户
				socket.to(sessionId).emit('user-joined', {
					id: socket.id,
					username
				});

				// 更新用户列表
				const sessionUsers = Array.from(this.users.values())
					.filter(u => u.sessionId === sessionId)
					.map(u => ({ id: u.id, username: u.username }));

				this.io.to(sessionId).emit('users-updated', sessionUsers);
				console.log(`用户 ${socket.id} (${username}) 加入会话 ${sessionId}`);
			});

			// 选择文件
			socket.on('select-file', (data) => {
				const user = this.users.get(socket.id);
				if (!user) return;

				user.fileId = data.fileId;
				console.log(`用户 ${user.username} 选择了文件 ${data.fileId}`);
			});

			// 文件变更
			socket.on('file-change', (data) => {
				const user = this.users.get(socket.id);
				if (!user) return;

				// 检查内容是否真的发生了变化
				const currentContent = this.fileContents.get(data.fileId);
				if (currentContent === data.content) {
					return; // 内容没有变化，不广播
				}

				this.fileContents.set(data.fileId, data.content);

				// 广播文件变更给其他用户
				socket.to(user.sessionId).emit('file-changed', {
					fileId: data.fileId,
					content: data.content,
					userId: socket.id,
					username: user.username,
					timestamp: new Date()
				});

				console.log(`用户 ${user.username} 修改了文件 ${data.fileId}`);
			});

			// 光标位置变更
			socket.on('cursor-change', (data) => {
				const user = this.users.get(socket.id);
				if (!user) return;

				// 验证光标数据格式
				if (!data.cursor || typeof data.cursor.line !== 'number' || typeof data.cursor.column !== 'number') {
					console.warn(`用户 ${user.username} 发送了无效的光标数据:`, data.cursor);
					return;
				}

				if (!this.cursors.has(data.fileId)) {
					this.cursors.set(data.fileId, new Map());
				}

				this.cursors.get(data.fileId).set(socket.id, data.cursor);

				// 广播光标位置给其他用户
				socket.to(user.sessionId).emit('cursor-changed', {
					fileId: data.fileId,
					cursor: data.cursor,
					userId: socket.id,
					username: user.username
				});
			});

			// 用户断开连接
			socket.on('disconnect', (reason) => {
				this.connectionCount--;
				const user = this.users.get(socket.id);
				if (user) {
					// 清理用户数据
					if (user.fileId && this.cursors.has(user.fileId)) {
						this.cursors.get(user.fileId).delete(socket.id);
					}
					this.users.delete(socket.id);

					// 通知其他用户
					socket.to(user.sessionId).emit('user-left', {
						userId: socket.id,
						username: user.username
					});

					// 更新用户列表
					const sessionUsers = Array.from(this.users.values())
						.filter(u => u.sessionId === user.sessionId)
						.map(u => ({ id: u.id, username: u.username }));

					this.io.to(user.sessionId).emit('users-updated', sessionUsers);
					console.log(`用户 ${socket.id} (${user.username}) 离开会话 ${user.sessionId}, 原因: ${reason}`);
				}

				console.log(`用户断开连接: ${socket.id} (总连接数: ${this.connectionCount}), 原因: ${reason}`);
			});

			// 监听连接错误
			socket.on('error', (error) => {
				console.error(`Socket ${socket.id} 错误:`, error);
			});
		});
	}

	// 获取会话中的在线用户
	getSessionUsers(sessionId) {
		return Array.from(this.users.values()).filter(u => u.sessionId === sessionId);
	}

	// 获取文件内容
	getFileContent(fileId) {
		return this.fileContents.get(fileId);
	}

	// 获取文件的所有光标位置
	getFileCursors(fileId) {
		const cursors = this.cursors.get(fileId);
		return cursors ? Array.from(cursors.entries()) : [];
	}

	// 获取连接统计
	getConnectionStats() {
		return {
			totalConnections: this.connectionCount,
			activeUsers: this.users.size,
			sessions: new Set(Array.from(this.users.values()).map(u => u.sessionId)).size
		};
	}
}

// 初始化WebSocket服务器
const wsServer = new WebSocketServer(io);

// 启动服务器
const PORT = process.env.PORT || 3001;
server.listen(PORT, () => {
	console.log(`🚀 WebSocket服务器启动成功!`);
	console.log(`📍 地址: http://localhost:${PORT}`);
	console.log(`🔌 WebSocket: ws://localhost:${PORT}/mock-websocket`);
});

// 优雅关闭
process.on('SIGTERM', () => {
	console.log('收到SIGTERM信号，正在关闭服务器...');
	server.close(() => {
		console.log('服务器已关闭');
		process.exit(0);
	});
});

process.on('SIGINT', () => {
	console.log('收到SIGINT信号，正在关闭服务器...');
	server.close(() => {
		console.log('服务器已关闭');
		process.exit(0);
	});
});