import { useEffect, useRef, useState, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';

interface User {
  id: string;
  username: string;
}

interface CursorPosition {
  line: number;
  column: number;
  username: string;
  userId: string;
  fileId?: string;
}

interface FileChange {
  fileId: string;
  content: string;
  userId: string;
  username: string;
  timestamp: Date;
}

interface UseCollaborationProps {
  sessionId: string;
  username: string;
  fileId?: string;
  onFileChange?: (change: FileChange) => void;
  onCursorChange?: (cursor: CursorPosition) => void;
  onUsersUpdate?: (users: User[]) => void;
  onUserJoined?: (user: User) => void;
  onUserLeft?: (user: User) => void;
}

export const useCollaboration = ({
                                   sessionId,
                                   username,
                                   fileId,
                                   onFileChange,
                                   onCursorChange,
                                   onUsersUpdate,
                                   onUserJoined,
                                   onUserLeft
                                 }: UseCollaborationProps) => {
  const [isConnected, setIsConnected] = useState(false);
  const [onlineUsers, setOnlineUsers] = useState<User[]>([]);
  const [cursors, setCursors] = useState<CursorPosition[]>([]);
  const socketRef = useRef<Socket | null>(null);

  // 使用 useRef 来稳定回调函数引用，避免不必要的重新连接
  const callbacksRef = useRef({
    onFileChange,
    onCursorChange,
    onUsersUpdate,
    onUserJoined,
    onUserLeft
  });

  // 更新回调函数引用
  useEffect(() => {
    callbacksRef.current = {
      onFileChange,
      onCursorChange,
      onUsersUpdate,
      onUserJoined,
      onUserLeft
    };
  }, [onFileChange, onCursorChange, onUsersUpdate, onUserJoined, onUserLeft]);

  // 连接WebSocket
  const connect = useCallback(() => {
    if (socketRef.current?.connected) return;

    const socket = io('https://card-dev-drcn.wallet.hihonorcloud.com', {
      path: '/mock-websocket',
      transports: ['websocket', 'polling'],
      // 添加重连配置，减少频繁重连
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 20000
    });

    socket.on('connect', () => {
      console.log('WebSocket connected');
      setIsConnected(true);

      // 加入会话
      socket.emit('join-session', { sessionId, username });
    });

    socket.on('disconnect', () => {
      console.log('WebSocket disconnected');
      setIsConnected(false);
    });

    socket.on('users-updated', (users: User[]) => {
      setOnlineUsers(users);
      callbacksRef.current.onUsersUpdate?.(users);
    });

    socket.on('user-joined', (user: User) => {
      callbacksRef.current.onUserJoined?.(user);
    });

    socket.on('user-left', (data: { userId: string, username: string }) => {
      // 清理离开用户的光标数据
      setCursors(prev => prev.filter(c => c.userId !== data.userId));
      callbacksRef.current.onUserLeft?.({ id: data.userId, username: data.username });
    });

    socket.on('file-changed', (change: FileChange) => {
      callbacksRef.current.onFileChange?.(change);
    });

    socket.on('cursor-changed', (data: { fileId: string, cursor: { line: number, column: number }, userId: string, username: string }) => {
      const cursorPosition: CursorPosition = {
        line: data.cursor.line,
        column: data.cursor.column,
        username: data.username,
        userId: data.userId,
        fileId: data.fileId
      };

      setCursors(prev => {
        const filtered = prev.filter(c => c.userId !== data.userId);
        return [...filtered, cursorPosition];
      });
      callbacksRef.current.onCursorChange?.(cursorPosition);
    });

    socketRef.current = socket;
  }, [sessionId, username]); // 只依赖 sessionId 和 username

  // 断开连接
  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
    }
  }, []);

  // 选择文件
  const selectFile = useCallback((fileId: string, content: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('select-file', { fileId, content });
    }
  }, []);

  // 发送文件变更
  const sendFileChange = useCallback((fileId: string, content: string) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('file-change', { fileId, content });
    }
  }, []);

  // 发送光标位置
  const sendCursorChange = useCallback((fileId: string, cursor: Omit<CursorPosition, 'username' | 'userId'>) => {
    if (socketRef.current?.connected) {
      socketRef.current.emit('cursor-change', {
        fileId,
        cursor: {
          line: cursor.line,
          column: cursor.column
        }
      });
    }
  }, []);

  // 连接和断开连接 - 只在组件挂载和卸载时执行
  useEffect(() => {
    connect();
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // 当fileId变化时，通知其他用户
  useEffect(() => {
    if (fileId && isConnected) {
      // 这里可以发送当前文件内容
      // selectFile(fileId, currentContent);
    }
  }, [fileId, isConnected]);

  return {
    isConnected,
    onlineUsers,
    cursors,
    selectFile,
    sendFileChange,
    sendCursorChange,
    disconnect
  };
};