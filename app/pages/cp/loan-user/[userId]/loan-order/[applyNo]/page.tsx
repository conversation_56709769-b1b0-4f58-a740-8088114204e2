"use client";

import {
  addToast,
  <PERSON>ton,
  Card,
  CardBody,
  CardHeader,
  Checkbox,
  CheckboxGroup,
  Chip,
  DatePicker,
  Divider, Input,
  NumberInput,
  Select,
  SelectItem,
  Spinner,
  Table,
  TableBody,
  TableCell,
  TableColumn,
  TableHeader,
  TableRow
} from "@heroui/react";
import { Icon } from "@iconify/react";
import { useParams, useRouter } from "next/navigation";
import React, { useEffect, useState } from "react";
import {
  LoanInfo,
  LoanOrderDetail,
  LoanStatusEnum, RepayErrorCodeEnum, RepayErrorMessageMap,
  RepayPlanTermDto, RepayStatusEnum, RepaySubmitEnum,
  TermStatusEnum
} from "@/types/cp/loanInfo";
import dayjs from "dayjs";
import {loanStatuses, repayMethods, repayResultStatuses, repaySubmitResultStatuses} from "@/types";
import { getLoanUserById, updateLoanUser } from "@/actions/cp/loanUserActions";
import { CalendarDate } from "@internationalized/date";
import { LoanUser } from "@/types/cp/loanUser";
import { CreditApplyStatusEnum, CreditInfo, RepayMethodEnum } from "@/types/cp/creditInfo";
import { BankCardInfo } from "@/types/cp/bankCard";
import { I18nProvider } from "@react-aria/i18n";
import RepayRecordTable from "@/components/cp/repayRecord/RepayRecordTable";
import { debounce } from "lodash";

export default function Page() {
  const { userId, applyNo } = useParams();
  const router = useRouter();
  const [order, setOrder] = useState<LoanOrderDetail | null>(null);
  const [effectiveDate, setEffectiveDate] = useState<CalendarDate | null>(null);
  const [loanUser, setLoanUser] = useState<LoanUser | null>(null);
  const [needResign, setNeedResign] = useState<boolean>(false);
  const [supportRepayType, setSupportRepayType] = useState<number[]>([]);
  const [currentTerm, setCurrentTerm] = useState<number | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const repayTypeOptions = [
    { value: 1, label: "提前还款" },
    { value: 2, label: "全部结清" },
    { value: 3, label: "还本期" },
    { value: 4, label: "还部分逾期" },
    { value: 5, label: "还逾期" },
    { value: 6, label: "去还款" },
  ];

  const handleSupportRepayTypeChange = (values: string[]) => {
    const numValues = values.map(Number);
    setSupportRepayType(numValues);
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        supportRepayType: numValues,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);
    }
  };

  const initial = async () => {
    if (order?.repayPlanTerms) {
      const term = findCurrentTerm(order.repayPlanTerms);
      setCurrentTerm(term);
    }

    setIsRefreshing(true);
    try {
      const user = await getLoanUserById(parseInt(userId as string));

      if (user) {
        const tempUser = {
          ...user,
          creditInfo: user.creditInfo as unknown as CreditInfo,
          bankCardInfo: user.bankCardInfo as unknown as BankCardInfo,
          loanInfo: user.loanInfo as unknown as LoanInfo,
        } as unknown as LoanUser;
        
        setLoanUser(tempUser);
        const loanOrder = tempUser.loanInfo?.status?.loanOrders.find((item) => {
          return item.applyNo === applyNo;
        });


        if (loanOrder) {
          setOrder(loanOrder);
          setSupportRepayType(loanOrder.supportRepayType || []);
          const date = dayjs(loanOrder.effectiveDate);
          const calendarDate = new CalendarDate(
            date.year(),
            date.month() + 1,
            date.date(),
          );
          setEffectiveDate(calendarDate);
          // 在初始加载时更新一次订单状态
          await updateOrderStatus(loanOrder,tempUser,  calendarDate);

        } else {
          setOrder(null);
        }
      }
    } finally {
      setIsRefreshing(false);
    }
  };

  useEffect(() => {
    initial();
  }, [applyNo]);

  // 计算还款计划的更新
  const calculateRepayPlanTerm = (
    term: RepayPlanTermDto,
    baseDate: dayjs.Dayjs,
    index: number,
    dayRate: string,
    today: dayjs.Dayjs
  ): {
    updatedTerm: RepayPlanTermDto;
    isOverdue: boolean;
  } => {
    // 如果已结清，只更新应还日期
    if (term.termStatus === TermStatusEnum.Settled) {
      return {
        updatedTerm: {
          ...term,
          shouldRepayDate: baseDate.add(index + 1, "month").format("YYYY-MM-DD"),
        },
        isOverdue: false,
      };
    }

    const shouldRepayDate = baseDate.add(index + 1, "month").format("YYYY-MM-DD");
    const isOverdue = dayjs(shouldRepayDate).isBefore(today, "day");
    const overdueDays = isOverdue ? today.diff(dayjs(shouldRepayDate), "day") : 0;

    let updatedTerm = { ...term, shouldRepayDate };

    if (isOverdue) {
      // 计算逾期相关费用
      updatedTerm.termPrinPenalty = Math.round(
        ((term.payableTermPrincipal || 0) * parseFloat(dayRate) * 1.3 * overdueDays) / 100
      );
      updatedTerm.termInterPenalty = Math.round(
        ((term.payableTermInterest || 0) * parseFloat(dayRate) * 1.3 * overdueDays) / 100
      );
      updatedTerm.termPenalty = updatedTerm.termPrinPenalty + updatedTerm.termInterPenalty;
      updatedTerm.termStatus = TermStatusEnum.Overdue;
    } else {
      updatedTerm.termStatus = TermStatusEnum.Unpaid;
    }

    // 计算应还金额
    updatedTerm.termAmount =
      updatedTerm.termPrincipal +
      updatedTerm.termInterest +
      (updatedTerm.termPenalty || 0) +
      (updatedTerm.payableTermViolateFee || 0);

    // 计算剩余应还金额
    updatedTerm.payableTermPrincipal = updatedTerm.termPrincipal - (updatedTerm.paidTermPrincipal || 0);
    updatedTerm.payableTermInterest = updatedTerm.termInterest - (updatedTerm.paidTermInterest || 0);
    updatedTerm.payableTermPenalty = (updatedTerm.termPenalty || 0) - (updatedTerm.paidTermPenalty || 0);
    updatedTerm.payableTermViolateFee = (updatedTerm.termViolateFee || 0) - (updatedTerm.paidTermViolateFee || 0);
    updatedTerm.payableTermAmount =
      updatedTerm.payableTermPrincipal +
      updatedTerm.payableTermInterest +
      updatedTerm.payableTermPenalty +
      updatedTerm.payableTermViolateFee;

    return { updatedTerm, isOverdue };
  };

  // 计算当前期的利息
  const calculateCurrentTermInterest = (
    term: RepayPlanTermDto,
    termStartDate: dayjs.Dayjs,
    today: dayjs.Dayjs,
    dayRate: string
  ): RepayPlanTermDto => {
    const days = today.diff(termStartDate, "day");
    const termInterest = Math.round((term.termPrincipal * parseFloat(dayRate) * days) / 100);

    return {
      ...term,
      termInterest,
      termAmount: term.termPrincipal + termInterest + (term.termPenalty || 0) + (term.payableTermViolateFee || 0),
      payableTermInterest: termInterest - (term.paidTermInterest || 0),
      payableTermAmount:
        (term.termPrincipal - (term.paidTermPrincipal || 0)) +
        (termInterest - (term.paidTermInterest || 0)) +
        ((term.termPenalty || 0) - (term.paidTermPenalty || 0)) +
        ((term.termViolateFee || 0) - (term.paidTermViolateFee || 0)),
    };
  };

  // 更新用户的订单信息
  const updateUserWithOrder = (user: LoanUser, order: LoanOrderDetail): LoanUser => {
    const updatedLoanInfo = {
      ...user.loanInfo,
      status: {
        ...user.loanInfo.status,
        loanOrders: user.loanInfo.status.loanOrders.map((item) =>
          item.applyNo === order.applyNo ? order : item
        ),
      },
    };

    return {
      ...user,
      loanInfo: updatedLoanInfo,
    };
  };

  const updateOrderStatus = async (loanOrder: LoanOrderDetail, tempUser: LoanUser, date: CalendarDate) => {
    if (!loanOrder || !tempUser || loanOrder.status === LoanStatusEnum.CLEARED) {
      return;
    }

    const baseDate = dayjs(`${date.year}-${date.month}-${date.day}`);
    const today = dayjs();
    let hasOverdue = false;

    // 一次性计算所有还款计划更新
    const updatedRepayPlanTerms = loanOrder.repayPlanTerms?.map((term, index) => {
      const { updatedTerm, isOverdue } = calculateRepayPlanTerm(
        term,
        baseDate,
        index,
        loanOrder.dayRate,
        today
      );
      
      if (isOverdue) {
        hasOverdue = true;
        updatedTerm.overdue = true;
      }
      
      return updatedTerm;
    });

    if (!updatedRepayPlanTerms) {
      return;
    }

    // 找到当前期数
    const newCurrentTerm = findCurrentTerm(updatedRepayPlanTerms);

    // 更新当前期的利息（如果需要）
    const finalRepayPlanTerms = updatedRepayPlanTerms.map((term) => {
      if (
        loanOrder.repayMethod !== RepayMethodEnum.FixedTerm &&
        newCurrentTerm === term.termNo
      ) {
        const termStartDate = dayjs(term.shouldRepayDate).subtract(1, "month");
        return calculateCurrentTermInterest(term, termStartDate, today, loanOrder.dayRate);
      }
      return term;
    });

    // 构建最终的订单状态
    const finalOrder: LoanOrderDetail = {
      ...loanOrder,
      effectiveDate: baseDate.format("YYYY-MM-DD"),
      applyDate: baseDate.format("YYYY-MM-DD"),
      applyTime: baseDate.valueOf(),
      repayPlanTerms: finalRepayPlanTerms,
      status: hasOverdue ? LoanStatusEnum.OVERDUE : LoanStatusEnum.REPAYING,
    };


    const finalLoanUser = {
      ...tempUser,
      loanInfo: {
        ...tempUser.loanInfo,
        status: {
          ...tempUser.loanInfo.status,
          loanOrders: tempUser.loanInfo.status.loanOrders.map((item) =>
            item.applyNo === finalOrder.applyNo ? finalOrder : item
          ),
        },
      },
    };

    // 批量更新状态
    setOrder(finalOrder);
    setCurrentTerm(newCurrentTerm);
    setLoanUser(finalLoanUser);
    
    // 最后一次性更新到服务器
    updateLoanUser(finalLoanUser);
  };

  const handleEffectiveDateChange =async (newDate: CalendarDate | null) => {
    // 如果选择的日期大于已选择的日期，则不更新
    if (newDate && effectiveDate && newDate.compare(effectiveDate) > 0) {
      addToast({
        title: "不能选择比当前日期更晚的日期",
        color: "danger",
      });
      return;
    }
    setEffectiveDate(newDate);
    if (newDate) {
      await updateOrderStatus(order, loanUser, newDate);
      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleNeedResignChange = (value: string) => {
    const newValue = value === "1";
    setNeedResign(newValue);
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        needResign: newValue,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);

      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleResignCheckResultChange = (value: string) => {
    const newValue = value === "1";
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        resignCheckResult: newValue,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        }
        };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);

      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleResignCheckErrorCodeChange = (value: string) => {
    const newValue = Number(value);
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        resignCheckErrorCode: newValue,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);

      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleResignCheckErrorMsgChange = (value: string) => {
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        resignCheckErrorMsg: value,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);

      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };


  const handleRepayResultStatusChange = (value: string) => {
    const newValue = Number(value) as RepaySubmitEnum;
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        repayResultStatus: newValue,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);
      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleCallBackDelayChange = (value: number) => {
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        callBackDelay: value,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);
      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleAutoCallbackChange = (value: boolean) => {
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        autoCallback: value,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);
      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleRepayErrorChange = (value: string) => {
    const newValue = value as RepayErrorCodeEnum;
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        refuseCode: RepayErrorMessageMap[newValue],
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);
      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleRepayQueryResultStatusChange = (value: string) => {
    const newValue = Number(value) as RepayStatusEnum;
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        repayQueryResult: newValue,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        }
        };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);
      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleRepayQueryResultErrorCodeChange = (value: string) => {
    const newValue = Number(value);
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        repayQueryResultErrorCode: newValue,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);
      addToast({
        title: "保存成功",
        color: "success",
      });
    }
  };

  const handleRepayQueryResultErrorMsgChange = (value: string) => {
    if (order && loanUser) {
      const updatedOrder = {
        ...order,
        repayQueryResultErrorMsg: value,
      };
      setOrder(updatedOrder);

      // 更新 loanUser 中的 loanOrder
      const loanInfo = loanUser.loanInfo;
      const updatedLoanInfo = {
        ...loanInfo,
        status: {
          ...loanInfo.status,
          loanOrders: loanInfo.status.loanOrders.map((item) =>
            item.applyNo === applyNo ? updatedOrder : item,
          ),
        },
      };
      const updatedLoanUser = {
        ...loanUser,
        loanInfo: updatedLoanInfo,
      };
      setLoanUser(updatedLoanUser);
      updateLoanUser(updatedLoanUser);
      addToast({
        title: "保存成功",
        color: "success",
      });
    }
    };

  const findCurrentTerm = (terms: RepayPlanTermDto[]) => {
    const today = dayjs();
    for (let i = 0; i < terms.length; i++) {
      const term = terms[i];
      const termDate = dayjs(term.shouldRepayDate);
      if (today.isBefore(termDate) || today.isSame(termDate, "day")) {
        return i + 1;
      }
    }
    return terms.length; // 如果当前日期超过最后一期，返回最后一期
  };


  if (!order) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Spinner size="lg" color="primary" />
      </div>
    );
  }

  const repayPlanColumns = [
    { name: "期数", uid: "termNo" },
    { name: "应还日期", uid: "shouldRepayDate" },
    { name: "总应还总额", uid: "termAmount", className: "bg-blue-200" },
    { name: "总应还本金", uid: "termPrincipal", className: "bg-blue-200" },
    { name: "总应还利息", uid: "termInterest", className: "bg-blue-200" },
    { name: "总应还罚息", uid: "termPenalty", className: "bg-blue-200" },
    { name: "总应还违约金", uid: "termViolateFee", className: "bg-blue-200" },
    { name: "实还总额", uid: "paidTermAmount", className: "bg-green-200" },
    { name: "实还本金", uid: "paidTermPrincipal", className: "bg-green-200" },
    { name: "实还利息", uid: "paidTermInterest", className: "bg-green-200" },
    { name: "实还罚息", uid: "paidTermPenalty", className: "bg-green-200" },
    {
      name: "实还违约金",
      uid: "paidTermViolateFee",
      className: "bg-green-200",
    },
    {
      name: "剩余应还总额",
      uid: "payableTermAmount",
      className: "bg-orange-200",
    },
    {
      name: "剩余应还本金",
      uid: "payableTermPrincipal",
      className: "bg-orange-200",
    },
    {
      name: "剩余应还利息",
      uid: "payableTermInterest",
      className: "bg-orange-200",
    },
    {
      name: "剩余应还罚息",
      uid: "payableTermPenalty",
      className: "bg-orange-200",
    },
    {
      name: "剩余应还违约金",
      uid: "payableTermViolateFee",
      className: "bg-orange-200",
    },
    { name: "状态", uid: "status" },
  ];

  const formatAmount = (amount?: number) => {
    if (amount === undefined) return "-";
    return (amount / 100).toFixed(2);
  };

  const renderCell = (term: RepayPlanTermDto, columnKey: string) => {
    switch (columnKey) {
      case "status":
        const isCurrentTerm = currentTerm === term.termNo;
        return (
          <div className="flex items-center gap-2">
            <Chip
              className="capitalize border-none gap-1 text-default-600"
              color={
                term.termStatus === TermStatusEnum.Overdue
                  ? "danger"
                  : term.termStatus === TermStatusEnum.Settled
                    ? "success"
                    : "default"
              }
              size="sm"
              variant="dot"
            >
              {term.termStatus === TermStatusEnum.Overdue
                ? "逾期"
                : term.termStatus === TermStatusEnum.Settled
                  ? "已结清"
                  : "待还款"}
            </Chip>
            {isCurrentTerm && (
              <Chip
                className="capitalize border-none gap-1"
                color="primary"
                size="sm"
                variant="flat"
              >
                当前期
              </Chip>
            )}
          </div>
        );
      default:
        return null;
    }
  };
  return (
    <div className="p-4">
      <div className="grid gap-4">
        {/* 基本信息 */}
        <Card>
          <CardBody>
            {/* 将返回和刷新按钮放到卡片顶部 */}
            <div className="mb-4 flex justify-between items-center">
              <Button
                color="primary"
                startContent={<Icon icon="mdi:arrow-left" className="text-xl" />}
                onPress={() => router.back()}
              >
                返回
              </Button>
              <Button
                color="primary"
                startContent={
                  isRefreshing ? (
                    <Spinner size="sm" color="white" />
                  ) : (
                    <Icon icon="mdi:refresh" className="text-xl" />
                  )
                }
                onPress={() => initial()}
                isIconOnly
                isDisabled={isRefreshing}
              />
            </div>
            {isRefreshing ? (
              <div className="flex justify-center items-center min-h-[200px]">
                <Spinner size="lg" />
              </div>
            ) : (
              <>
                <div className="flex gap-3 mb-4 items-center">
                  <Icon icon="mdi:information" className="text-xl" />
                  <div className={"text-xl"}>基本信息</div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-500 mb-1">荣耀侧订单号</p>
                    <p>{order.applyNo}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">借款状态</p>
                    <p>
                      {
                        loanStatuses.find((status) => status.key === order.status)
                          ?.label
                      }
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">
                      起息日期(不能选择比当前日期更晚的日期)
                    </p>
                    <I18nProvider locale="zh-CN">
                      <DatePicker
                        value={effectiveDate}
                        onChange={handleEffectiveDateChange}
                        className="max-w-xs"
                      />
                    </I18nProvider>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">借款金额</p>
                    <p>{formatAmount(order.loanAmount)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">借款期数</p>
                    <p>{order.totalTerm}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">产品类型</p>
                    <p>
                      {
                        repayMethods.find(
                          (method) => method.key === order.repayMethod,
                        )?.label
                      }
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-500 mb-1">支持的还款类型</p>
                    <CheckboxGroup
                      orientation="horizontal"
                      value={supportRepayType.map(String)}
                      onValueChange={handleSupportRepayTypeChange}
                      className="flex flex-col gap-2"
                    >
                      {repayTypeOptions.map((option) => (
                        <Checkbox key={option.value} value={String(option.value)}>
                          {option.label}
                        </Checkbox>
                      ))}
                    </CheckboxGroup>
                  </div>
                </div>

                <Divider className={"my-4"}/>

                <div className="flex gap-3 mb-4 items-center">
                  <Icon icon="mdi:account-check" className="text-xl" />
                  <div className={"text-xl"}>重新签约结果设置</div>
                </div>

                <div className="grid grid-cols-4 gap-4">
                  <Select
                    label="重新签约检查结果"
                    defaultSelectedKeys={[order.resignCheckResult || order.resignCheckResult == null ? "1" : "0"]}
                    onChange={(e) => {
                      handleResignCheckResultChange(e.target.value);
                    }}
                  >
                    <SelectItem key="0">失败</SelectItem>
                    <SelectItem key="1">成功</SelectItem>
                  </Select>


                  {(order.resignCheckResult || order.resignCheckResult == null) && (
                    <Select
                      label="是否需要重新签约"
                      defaultSelectedKeys={[order.needResign ? "1" : "0"]}
                      onChange={(e) => handleNeedResignChange(e.target.value)}
                    >
                      <SelectItem key="0">不需要</SelectItem>
                      <SelectItem key="1">需要</SelectItem>
                    </Select>)}

                  {order.resignCheckResult === false && (
                    <>
                      <Input
                        label="失败错误码"
                        defaultValue={order.resignCheckErrorCode?.toString() || ""}
                        onValueChange={(value) => {
                          handleResignCheckErrorCodeChange(value);
                        }}
                      />

                      <Input
                        label="失败错误信息"
                        defaultValue={order.resignCheckErrorMsg || ""}
                        onValueChange={(value) => {
                          handleResignCheckErrorMsgChange(value);
                        }}
                      />
                    </>
                  )}
                </div>

                <Divider className={"my-4"}/>
                <div className="flex gap-3 mb-4 items-center">
                  <Icon icon="mdi:cash-multiple" className="text-xl" />
                  <div className={"text-xl"}>还款结果设置</div>
                </div>

                <div className="grid grid-cols-4 gap-4 mb-3">
                  <Select
                    label="还款提交结果"
                    defaultSelectedKeys={[order.repayResultStatus?.toString()]}
                    onSelectionChange={e => handleRepayResultStatusChange(e.anchorKey as string)}
                  >
                    {repaySubmitResultStatuses.map((status) => (
                      <SelectItem key={status.key}>{status.label}</SelectItem>
                    ))}
                  </Select>


                  {order.repayResultStatus === RepaySubmitEnum.Failed && (
                    <Select
                      label="失败原因"
                      defaultSelectedKeys={[order.refuseCode]}
                      onSelectionChange={e => handleRepayErrorChange(e.anchorKey as string)}
                    >
                      {Object.entries(RepayErrorCodeEnum).map(([key, value]) => (
                        <SelectItem key={value}>
                          {RepayErrorMessageMap[value]}
                        </SelectItem>
                      ))}
                    </Select>
                  )}
                </div>

                {order.repayResultStatus === RepaySubmitEnum.Success && (
                  <div className="grid grid-cols-4 gap-4 mb-3">
                    <Select
                      label="还款审批结果"
                      defaultSelectedKeys={[order.repayQueryResult?.toString() == null ? RepayStatusEnum.Success.toString() : order.repayQueryResult?.toString()]}
                      onSelectionChange={e => handleRepayQueryResultStatusChange(e.anchorKey as string)}
                    >
                      {repayResultStatuses.map((status) => (
                        <SelectItem key={status.key}>{status.label}</SelectItem>
                      ))}
                    </Select>


                    {order.repayQueryResult === RepayStatusEnum.Failed && (
                      <>
                        <Input
                          label="失败错误码"
                          defaultValue={order.repayQueryResultErrorCode?.toString() || ""}
                          onValueChange={(value) => {
                            debounce(handleRepayQueryResultErrorCodeChange, 1000)(value);
                          }}
                        />

                        <Input
                          label="失败错误信息"
                          defaultValue={order.repayQueryResultErrorMsg || ""}
                          onValueChange={(value) => {
                            debounce(handleRepayQueryResultErrorMsgChange, 1000)(value);
                          }}
                        />
                      </>
                    )}
                  </div>
                )}


                <div className="grid grid-cols-4 gap-4  ">
                  {order.repayQueryResult!== RepayStatusEnum.Repaying && (
                    <Select
                      label="是否自动触发回调"
                      defaultSelectedKeys={[
                        order.autoCallback ? "1" : "0",
                      ]}
                      onSelectionChange={e => handleAutoCallbackChange(e.anchorKey === "1")}
                    >
                      <SelectItem key="1">是</SelectItem>
                      <SelectItem key="0">否</SelectItem>
                    </Select>
                  )}

                  {order.repayQueryResult!== RepayStatusEnum.Repaying &&
                    order.autoCallback && (
                      <NumberInput
                        label={"延时(单位：秒)"}
                        defaultValue={order.callBackDelay}
                        onValueChange={e => handleCallBackDelayChange(e)}
                      />)}
                </div>
              </>
            )}
          </CardBody>
        </Card>

        {!isRefreshing && (
          <>
            <RepayRecordTable
              records={
                loanUser?.loanInfo.status.repayRecords.filter(
                  (record) => record.outOrderNo === order?.outOrderNo
                ) || []
              }
              loanUser={loanUser!}
              onRefresh={initial}
            />

            {/* 还款计划 */}
            <Table
              aria-label="还款计划"
              topContent={<div className={"text-xl"}>还款计划</div>}
            >
              <TableHeader columns={repayPlanColumns}>
                {(column) => (
                  <TableColumn key={column.uid} className={column.className}>
                    {column.name}
                  </TableColumn>
                )}
              </TableHeader>
              <TableBody items={order.repayPlanTerms}>
                {(term: RepayPlanTermDto) => (
                  <TableRow key={term.termNo}>
                    <TableCell>{term.termNo}</TableCell>
                    <TableCell>
                      {dayjs(term.shouldRepayDate).format("YYYY-MM-DD")}
                    </TableCell>
                    <TableCell>{formatAmount(term.termAmount)}</TableCell>
                    <TableCell>{formatAmount(term.termPrincipal)}</TableCell>
                    <TableCell>{formatAmount(term.termInterest)}</TableCell>
                    <TableCell>{formatAmount(term.termPenalty)}</TableCell>
                    <TableCell>{formatAmount(term.termViolateFee)}</TableCell>
                    <TableCell>{formatAmount(term.paidTermAmount)}</TableCell>
                    <TableCell>
                      {formatAmount(term.paidTermPrincipal)}
                    </TableCell>
                    <TableCell>{formatAmount(term.paidTermInterest)}</TableCell>
                    <TableCell>{formatAmount(term.paidTermPenalty)}</TableCell>
                    <TableCell>
                      {formatAmount(term.paidTermViolateFee)}
                    </TableCell>
                    <TableCell>
                      {formatAmount(term.payableTermAmount)}
                    </TableCell>
                    <TableCell>
                      {formatAmount(term.payableTermPrincipal)}
                    </TableCell>
                    <TableCell>
                      {formatAmount(term.payableTermInterest)}
                    </TableCell>
                    <TableCell>
                      {formatAmount(term.payableTermPenalty)}
                    </TableCell>
                    <TableCell>
                      {formatAmount(term.payableTermViolateFee)}
                    </TableCell>
                    <TableCell>{renderCell(term, "status")}</TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </>
        )}
      </div>
    </div>
  );
}
