import { decrypt, decryptByPrivate<PERSON>ey, encryptRes } from "@/utils/decrypt";
import { methodMap, MethodName } from "@/app/cp/methodMap";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { AsyncLocalStorage } from "async_hooks";
import {InterfaceResult, InterfaceResultStatusEnum} from "@/types/cp/interfaceResult";
import {addInterfaceLog} from "@/actions/cp/interfaceLogAction";

// 创建 AsyncLocalStorage 实例
const asyncLocalStorage = new AsyncLocalStorage<{
  userId: number;
  interfaceName: string;
  decryptParams: any;
}>();

// 创建上下文对象
const context = {
  userId: 0,
  interfaceName: "",
  decryptParams: null as any,
};

export async function POST(
  req: Request,
  { params }: { params: Promise<{ sceneId: string }> },
) {
  const context = {
    userId: 0,
    interfaceName: "",
    decryptParams: null,
  };

  return asyncLocalStorage.run(context, async () => {
    try {
      const requestBody = await req.json();

      const key = requestBody.key;
      const decryptKey = decryptByPrivateKey(key);
      const EncryptParams = requestBody.params;
      const decryptParams = JSON.parse(decrypt(EncryptParams, decryptKey));
      const method: MethodName = requestBody.method;

      // 存储上下文信息
      const userId = decryptParams.userId;
      let user;
      if (userId) {
        user = await getLoanUserByUserId(userId);
      }
      console.log("请求接口:", method);

      // 更新当前请求的上下文
      const currentContext = asyncLocalStorage.getStore();
      if (currentContext) {
        currentContext.userId = user ? user.id : 0;
        currentContext.interfaceName = method;
        currentContext.decryptParams = decryptParams;
      }

      if (user) {
        const interfaceResults = user.interfaceResults as unknown as InterfaceResult[];
        const targetInterfaceResult = interfaceResults?.find(
          (item) => item.key === method,
        );
        if (targetInterfaceResult && targetInterfaceResult.status === InterfaceResultStatusEnum.FAILED
        ) {
          addInterfaceLog({
            userId: user.id,
            interfaceName: method,
            requestData: JSON.stringify(decryptParams),
            responseData: JSON.stringify({
              code: targetInterfaceResult.errorCode,
              message: targetInterfaceResult.errorMessage,
            }),
          });
          return Response.json({
            code: targetInterfaceResult.errorCode,
            message: targetInterfaceResult.errorMessage,
          });
        }else if (targetInterfaceResult && targetInterfaceResult.status === InterfaceResultStatusEnum.SUCCESS
          && targetInterfaceResult.responseData
        ) {
          addInterfaceLog({
            userId: user.id,
            interfaceName: method,
            requestData: JSON.stringify(decryptParams),
            responseData: targetInterfaceResult.responseData,
          });
          return Response.json(encryptRes(JSON.parse(targetInterfaceResult.responseData)));
        }
      }
      const res = await methodMap[method](decryptParams);

      return Response.json(res);
    } catch (error) {
      console.error("Request error:", error);
      return Response.json({
        code: 1,
        message: "请求处理失败",
      });
    }
  });
}

// 导出上下文对象，供其他模块使用
export { context };

// 导出一个获取当前请求上下文的方法
export function getRequestContext() {
  return asyncLocalStorage.getStore();
}
