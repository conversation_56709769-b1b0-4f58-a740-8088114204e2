import { encryptRes } from "@/utils/decrypt";
import { addInterfaceLog } from "@/actions/cp/interfaceLogAction";
import { getLoanUserByUserId } from "@/actions/cp/loanUserActions";
import { LoanInfo } from "@/types/cp/loanInfo";
import dayjs from "dayjs";

const interfaceName = "loan.record.detail";

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 荣耀侧借款订单号 */
  applyNo: string;
}

export interface RepayPlanTermDto {
  /** 期次 */
  termNo: number;
  /** 应还款日期，yyyyMMdd */
  shouldRepayDate: string;
  /** 当期状态 */
  termStatus: number;
  /** 还款类型 */
  repayCategory?: number;
  /** 本期应还总额 */
  termAmount: number;
  /** 本期应还本金 */
  termPrincipal: number;
  /** 本期应还利息 */
  termInterest: number;
  /** 本期应还罚息 */
  termPenalty: number;
  /** 本期应还本金罚息 */
  termPrinPenalty: number;
  /** 本期应还利息罚息 */
  termInterPenalty: number;
  /** 本期应还逾期费 */
  termOverdueFee: number;
  /** 本期应还服务费 */
  termServiceFee: number;
  /** 本期应还违约金 */
  termViolateFee: number;
  /** 本期优惠券减免金额 */
  termReductionAmount: number;
  /** 本期实际还款日 */
  paidTime?: number;
  /** 本期实还总额 */
  paidTermAmount?: number;
  /** 本期实还本金 */
  paidTermPrincipal?: number;
  /** 本期实还利息 */
  paidTermInterest?: number;
  /** 本期实还优惠券额 */
  paidTermReductionAmount?: number;
  /** 本期实还罚息 */
  paidTermPenalty?: number;
  /** 本期实还本金罚息 */
  paidTermPrinPenalty?: number;
  /** 本期实还利息罚息 */
  paidTermInterPenalty?: number;
  /** 本期实还逾期费 */
  paidTermOverdueFee?: number;
  /** 本期实还服务费 */
  paidTermServiceFee?: number;
  /** 本期实还违约金 */
  paidTermViolateFee?: number;
  /** 剩余应还还款金额 */
  payableTermAmount?: number;
  /** 剩余应还还款本金 */
  payableTermPrincipal?: number;
  /** 剩余应还还款利息 */
  payableTermInterest?: number;
  /** 剩余应还罚息 */
  payableTermPenalty?: number;
  /** 剩余应还本金罚息 */
  payableTermPrinPenalty?: number;
  /** 剩余应还利息罚息 */
  payableTermInterPenalty?: number;
  /** 剩余应还逾期费 */
  payableTermOverdueFee?: number;
  /** 剩余应还服务费 */
  payableTermServiceFee?: number;
  /** 剩余应还违约金 */
  payableTermViolateFee?: number;
  /** 逾期天数 */
  overdueDays?: number;
  /** 逾期金额 */
  overdueAmount?: number;
  /** 是否提前还款 */
  preRepay?: boolean;
  /** 是否逾期 */
  overdue: boolean;
  /** 优惠信息说明 */
  reductionAmountDesc?: string;
}

export interface ContractDto {
  /** 协议ID */
  contractId: string;
  /** 合同名称 */
  contractName: string;
  /** 合同地址 */
  contractUrl: string;
}

export interface ResponseDataType {
  /** 渠道方借款订单号 */
  outOrderNo: string;
  /** 荣耀侧借款申请订单号 */
  applyNo: string;
  /** 借款申请日期，yyyyMMdd */
  applyDate: string;
  /** 借款申请时间，毫秒 */
  applyTime: number;
  /** 借款起息日 */
  effectiveDate?: string;
  /** 借款金额，单位：分 */
  loanAmount: number;
  /** 借款状态 */
  status: number;
  /** 借款期数 */
  totalTerm: number;
  /** 还款方式 */
  repayMethod: number;
  /** 借款来源 */
  loanSource?: string;
  /** 机构名称 */
  institutionNames?: string;
  /** 结清时间 */
  clearTime: number;
  /** 已还总金额 */
  paidAmount: number;
  /** 已还本金总额 */
  paidPrinAmount: number;
  /** 已还利息总额 */
  paidInterAmount: number;
  /** 已还服务费总额 */
  paidServiceFee: number;
  /** 已还罚息 */
  paidPenalty: number;
  /** 收款卡号（后四位） */
  bindCardNo: string;
  /** 收款卡发卡行code */
  bindBankCode: string;
  /** 收款卡发卡行名称 */
  bindBankName: string;
  /** 优惠券id */
  couponNo?: string;
  /** 日利率 */
  dayRate?: string;
  /** 月利率 */
  monthRate?: string;
  /** 年利率 */
  apr: string;
  /** 优惠金额 */
  reductionAmount: number;
  /** 提前还款违约金 */
  prePenalty: number;
  /** 还款计划列表 */
  repayPlanTerms: RepayPlanTermDto[];
  /** 已签署借款协议列表 */
  contractList: ContractDto[];
  /** 借据逾期天数 */
  overdueDays?: number;
  /** 借据逾期未还金额 */
  overdueAmount?: number;
}

export async function loanRecordDetail(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  if (!loanInfo?.status?.loanOrders) {
    return encryptRes({
      code: 1,
      message: "未找到借款记录",
      data: null,
    });
  }

  const order = loanInfo.status.loanOrders.find(
    (order) => order.applyNo === params.applyNo,
  );

  if (!order) {
    return encryptRes({
      code: 1,
      message: "未找到指定借款记录",
      data: null,
    });
  }

  // 计算已还金额信息
  const paidAmount =
    order.repayPlanTerms?.reduce(
      (sum, term) => sum + (term.paidTermAmount || 0),
      0,
    ) ?? 0;
  const paidPrinAmount =
    order.repayPlanTerms?.reduce(
      (sum, term) => sum + (term.paidTermPrincipal || 0),
      0,
    ) ?? 0;
  const paidInterAmount =
    order.repayPlanTerms?.reduce(
      (sum, term) => sum + (term.paidTermInterest || 0),
      0,
    ) ?? 0;
  const paidServiceFee =
    order.repayPlanTerms?.reduce(
      (sum, term) => sum + (term.paidTermServiceFee || 0),
      0,
    ) ?? 0;
  const paidPenalty =
    order.repayPlanTerms?.reduce(
      (sum, term) => sum + (term.paidTermPenalty || 0),
      0,
    ) ?? 0;

  // 计算逾期信息
  const today = dayjs();
  const overdueTerms = order.repayPlanTerms?.filter((term) =>
    dayjs(term.shouldRepayDate).isBefore(today),
  );
  const overdueDays = overdueTerms?.length
    ? dayjs().diff(dayjs(overdueTerms[0].shouldRepayDate), "day")
    : undefined;
  const overdueAmount = overdueTerms?.reduce(
    (sum, term) =>
      sum +
      (term.termPrincipal || 0) +
      (term.termInterest || 0) +
      (term.termPenalty || 0),
    0,
  );

  const responseData: ResponseDataType = {
    outOrderNo: order.outOrderNo,
    applyNo: order.applyNo,
    applyDate: dayjs(order.applyTime).format("YYYYMMDD"),
    applyTime: dayjs(order.applyTime).valueOf(),
    effectiveDate: order.effectiveDate
      ? dayjs(order.effectiveDate).format("YYYYMMDD")
      : undefined,
    loanAmount: order.loanAmount,
    status: order.status,
    totalTerm: order.totalTerm,
    repayMethod: order.repayMethod,
    loanSource: order.loanSource,
    institutionNames: order.institutionNames,
    clearTime: order.clearTime || 0,
    paidAmount,
    paidPrinAmount,
    paidInterAmount,
    paidServiceFee,
    paidPenalty,
    bindCardNo: order.bindCardNo,
    bindBankCode: order.bindBankCode,
    bindBankName: order.bindBankName,
    couponNo: order.couponNo,
    dayRate: order.dayRate,
    monthRate: order.monthRate,
    apr: order.apr,
    reductionAmount: order.reductionAmount || 0,
    prePenalty: order.prePenalty || 0,
    repayPlanTerms:
      order.repayPlanTerms?.map((term) => ({
        termNo: term.termNo,
        shouldRepayDate: dayjs(term.shouldRepayDate).format("YYYYMMDD"),
        termStatus: term.termStatus,
        repayCategory: term.repayCategory,
        termAmount: term.termAmount || 0,
        termPrincipal: term.termPrincipal || 0,
        termInterest: term.termInterest || 0,
        termPenalty: term.termPenalty || 0,
        termPrinPenalty: term.termPrinPenalty || 0,
        termInterPenalty: term.termInterPenalty || 0,
        termOverdueFee: term.termOverdueFee || 0,
        termServiceFee: term.termServiceFee || 0,
        termViolateFee: term.termViolateFee || 0,
        termReductionAmount: term.termReductionAmount || 0,
        paidTime: term.paidTime,
        paidTermAmount: term.paidTermAmount,
        paidTermPrincipal: term.paidTermPrincipal,
        paidTermInterest: term.paidTermInterest,
        paidTermReductionAmount: term.paidTermReductionAmount,
        paidTermPenalty: term.paidTermPenalty,
        paidTermPrinPenalty: term.paidTermPrinPenalty,
        paidTermInterPenalty: term.paidTermInterPenalty,
        paidTermOverdueFee: term.paidTermOverdueFee,
        paidTermServiceFee: term.paidTermServiceFee,
        paidTermViolateFee: term.paidTermViolateFee,
        payableTermAmount: (term.termAmount || 0) - (term.paidTermAmount || 0),
        payableTermPrincipal: (term.termPrincipal || 0) - (term.paidTermPrincipal || 0),
        payableTermInterest: (term.termInterest || 0) - (term.paidTermInterest || 0),
        payableTermPenalty: (term.termPenalty || 0) - (term.paidTermPenalty || 0),
        payableTermPrinPenalty: (term.termPrinPenalty || 0) - (term.paidTermPrinPenalty || 0),
        payableTermInterPenalty: (term.termInterPenalty || 0) - (term.paidTermInterPenalty || 0),
        payableTermOverdueFee: (term.termOverdueFee || 0) - (term.paidTermOverdueFee || 0),
        payableTermServiceFee: (term.termServiceFee || 0) - (term.paidTermServiceFee || 0),
        payableTermViolateFee: (term.termViolateFee || 0) - (term.paidTermViolateFee || 0),
        overdueDays: overdueTerms?.find((t) => t.termNo === term.termNo)
          ? dayjs().diff(dayjs(term.shouldRepayDate), "day")
          : 0,
        overdueAmount: overdueTerms?.find((t) => t.termNo === term.termNo)
          ? (term.termPrincipal || 0) +
            (term.termInterest || 0) +
            (term.termPenalty || 0)
          : 0,
        preRepay: term.preRepay,
        overdue: term.overdue,
        reductionAmountDesc: term.reductionAmountDesc,
      })) || [],
    contractList:
      order.contractList?.map((contract) => ({
        contractId: contract.contractId,
        contractName: contract.contractName,
        contractUrl: contract.contractUrl,
      })) || [],
    overdueDays,
    overdueAmount,
  };

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  addInterfaceLog({
    userId: user.id,
    interfaceName,
    requestData: JSON.stringify(params),
    responseData: JSON.stringify(res),
  });

  return encryptRes(res);
}
