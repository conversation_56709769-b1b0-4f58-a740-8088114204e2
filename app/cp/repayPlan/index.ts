import { encryptRes } from "@/utils/decrypt";
import {
  getLoanUserByUserId,
  updateLoanUser,
} from "@/actions/cp/loanUserActions";
import {
  LoanInfo,
  LoanStatusEnum,
  RepayTypeEnum,
  TermStatusEnum,
} from "@/types/cp/loanInfo";
import dayjs from "dayjs";
import isSameOrAfter from "dayjs/plugin/isSameOrAfter";
import { RepayMethodEnum } from "@/app/cp/creditInfoQuery";

dayjs.extend(isSameOrAfter);

export interface ParamType {
  /** 荣耀方用户id */
  userId: string;
  /** 荣耀侧借款订单号，不传则查询所有待还计划 */
  applyNo?: string;
}

export interface ResponseDataType {
  /** 记录总条数 */
  totalNum: number;
  /** 记录列表 */
  records: RepayPlanDto[];
}

export interface RepayPlanTermDto {
  /** 期次 */
  termNo: number;
  /** 应还款日期，yyyyMMdd */
  shouldRepayDate: string;
  /** 本期应还总额,单位：分 */
  termAmount: number;
  /** 本期应还本金，单位：分 */
  termPrincipal: number;
  /** 本期应还利息，单位：分 */
  termInterest: number;
  /** 本期优惠券减免金额，单位：分 */
  termReductionAmount: number;
  /** 本期应还罚息，单位：分 */
  termPenalty: number;
  /** 本期应还本金罚息，单位：分 */
  termPrinPenalty: number;
  /** 本期应还利息罚息，单位：分 */
  termInterPenalty: number;
  /** 本期应还逾期费，单位：分 */
  termOverdueFee: number;
  /** 本期应还违约金，单位：分 */
  termViolateFee: number;
  /** 本期应还服务费，单位：分 */
  termServiceFee: number;
  /** 逾期天数 */
  overdueDays: number;
}

export interface RepayPlanDto {
  /** 荣耀侧借款申请订单号 */
  applyNo: string;
  /** 渠道方借款订单号 */
  outOrderNo: string;
  /** 借款金额，单位：分 */
  loanAmount: number;
  /** 可还款状态，2-正常还款中，7-禁还期 */
  status: number;
  /** 借款期数 */
  totalTerm: number;
  /** 当前期次 */
  currentTerm: number;
  /** 剩余应还总额，单位：分 */
  duerepay: number;
  /** 放款日期,yyyyMMdd */
  paydate: string;
  /** 还款计划列表（不含已还期次） */
  repayPlanTerms: RepayPlanTermDto[];
  /** 借据逾期天数，有逾期时必传 */
  overdueDays?: number;
  /** 借据逾期未还金额（本息罚-优惠金额），有逾期时必传 */
  overdueAmount?: number;
  /** 支持的还款类型列表 */
  supportRepayType: number[];
}

export async function repayPlan(params: ParamType) {
  const user = await getLoanUserByUserId(params.userId);

  if (!user) {
    console.log("用户不存在");
    return encryptRes({
      code: 1,
      message: "用户不存在",
      data: null,
    });
  }

  const loanInfo = user.loanInfo as unknown as LoanInfo;
  if (!loanInfo?.status?.loanOrders) {
    return encryptRes({
      code: 0,
      message: "success",
      data: {
        totalNum: 0,
        records: [],
      },
    });
  }

  // 过滤出未结清的订单
  const activeOrders = loanInfo.status.loanOrders.filter(
    (order) =>
      order.status !== LoanStatusEnum.CLEARED &&
      (!params.applyNo || order.applyNo === params.applyNo),
  );

  const records: RepayPlanDto[] = activeOrders.map((order) => {
    // 计算当前期次
    const today = dayjs();

    const currentTerm =
      order.repayPlanTerms?.findIndex((term) =>
        dayjs(term.shouldRepayDate).isSameOrAfter(today, "day"),
      ) ?? -1;

    // 更新利息计算
    const updatedTerms = order.repayPlanTerms?.map((term) => {
      if (term.termStatus === TermStatusEnum.Settled) {
        return term;
      }

      const isOverdue = dayjs(term.shouldRepayDate).isBefore(today, "day");
      if (isOverdue) {
        const overdueDays = today.diff(dayjs(term.shouldRepayDate), "day");
        term.termPrinPenalty = Math.round(
          ((term.payableTermPrincipal || 0) *
            parseFloat(order.dayRate) *
            1.3 *
            overdueDays) /
            100,
        );

        term.termInterPenalty = Math.round(
          ((term.payableTermInterest || 0) *
            parseFloat(order.dayRate) *
            1.3 *
            overdueDays) /
            100,
        );
      }

      // 计算剩余应还金额
      term.payableTermPrincipal =
        term.termPrincipal - (term.paidTermPrincipal || 0);
      term.payableTermInterest =
        term.termInterest - (term.paidTermInterest || 0);
      term.payableTermPenalty =
        (term.termPenalty || 0) - (term.paidTermPenalty || 0);
      term.payableTermViolateFee =
        (term.termViolateFee || 0) - (term.paidTermViolateFee || 0);
      term.payableTermAmount =
        term.payableTermPrincipal +
        term.payableTermInterest +
        term.payableTermPenalty +
        term.payableTermViolateFee;

      return term;
    });

    // 计算剩余应还总额
    const duerepay =
      updatedTerms?.reduce(
        (sum, term) => sum + (term.payableTermAmount || 0),
        0,
      ) ?? 0;

    // 计算逾期信息
    const overdueTerms =
      updatedTerms?.filter(
        (term) =>
          dayjs(term.shouldRepayDate).isBefore(today, "day") &&
          term.termStatus !== TermStatusEnum.Settled,
      ) ?? [];
    const overdueDays =
      overdueTerms.length > 0
        ? dayjs().diff(dayjs(overdueTerms[0].shouldRepayDate), "day")
        : 0;
    const overdueAmount = overdueTerms.reduce(
      (sum, term) => sum + (term.payableTermAmount || 0),
      0,
    );

    // 确定支持的还款类型
    const supportRepayType: number[] = [];

    const supportRepayTypes = order.supportRepayType ?? [];

    if (supportRepayTypes.includes(RepayTypeEnum.GoRepay)) {
      supportRepayType.push(RepayTypeEnum.GoRepay);
    }

    if (overdueTerms.length > 0) {
      if (supportRepayTypes.includes(RepayTypeEnum.PartialOverdue)) {
        supportRepayType.push(RepayTypeEnum.PartialOverdue);
      }
      if (supportRepayTypes.includes(RepayTypeEnum.Overdue)) {
        supportRepayType.push(RepayTypeEnum.Overdue);
      }
    } else {
      // 检查是否有当前期的还款日
      const hasCurrentTermDue =
        updatedTerms?.some((term) => {
          const repayDate = dayjs(term.shouldRepayDate);
          return (
            repayDate.isSame(today, "day") &&
            !term.overdue &&
            term.termStatus !== TermStatusEnum.Settled
          );
        }) ?? false;

      if (
        order.repayMethod !== RepayMethodEnum.FixedTerm &&
        supportRepayTypes.includes(RepayTypeEnum.PrePayment)
      ) {
        supportRepayType.push(RepayTypeEnum.PrePayment);
      }
      if (supportRepayTypes.includes(RepayTypeEnum.FullSettlement)) {
        supportRepayType.push(RepayTypeEnum.FullSettlement);
      }
      if (
        hasCurrentTermDue &&
        supportRepayTypes.includes(RepayTypeEnum.PayThisTerm)
      ) {
        supportRepayType.push(RepayTypeEnum.PayThisTerm);
      }
    }

    return {
      applyNo: order.applyNo,
      outOrderNo: order.outOrderNo,
      loanAmount: order.loanAmount,
      status:
        order.status === LoanStatusEnum.REPAYING ||
        order.status === LoanStatusEnum.OVERDUE
          ? 2
          : 7,
      totalTerm: order.totalTerm,
      currentTerm: currentTerm === -1 ? order.totalTerm : currentTerm + 1,
      duerepay,
      paydate: dayjs(order.effectiveDate).format("YYYYMMDD"),
      overdueDays: overdueDays > 0 ? overdueDays : undefined,
      overdueAmount: overdueAmount > 0 ? overdueAmount : undefined,
      supportRepayType,
      repayPlanTerms:
        updatedTerms
          ?.filter((term) => term.termStatus !== TermStatusEnum.Settled)
          .map((term) => ({
            termNo: term.termNo,
            shouldRepayDate: dayjs(term.shouldRepayDate).format("YYYYMMDD"),
            termAmount: term.payableTermAmount || 0,
            termPrincipal: term.payableTermPrincipal || 0,
            termInterest: term.payableTermInterest || 0,
            termReductionAmount: term.termReductionAmount || 0,
            termPenalty: term.payableTermPenalty || 0,
            termPrinPenalty: term.payableTermPrinPenalty || 0,
            termInterPenalty: term.payableTermInterPenalty || 0,
            termOverdueFee: term.payableTermOverdueFee || 0,
            termViolateFee: term.payableTermViolateFee || 0,
            termServiceFee: term.payableTermServiceFee || 0,
            overdueDays: dayjs().isAfter(dayjs(term.shouldRepayDate))
              ? dayjs().diff(dayjs(term.shouldRepayDate), "day")
              : 0,
          })) || []
    };
  });

  updateLoanUser(user);

  const responseData: ResponseDataType = {
    totalNum: records.length,
    records,
  };

  const res = {
    code: 0,
    message: "success",
    data: responseData,
  };

  return encryptRes(res);
}